# نظام إدارة المستخدمين API

نظام شامل لإدارة المستخدمين والأدوار والصلاحيات باستخدام Django REST Framework مع JWT Authentication و Repository Pattern.

## 🚀 المميزات

### ✅ المكونات الأساسية
- **نظام مصادقة JWT** - تسجيل دخول آمن مع Access و Refresh Tokens
- **إدارة المستخدمين** - CRUD كامل للمستخدمين مع البحث والفلترة
- **نظام الأدوار والصلاحيات** - RBAC (Role-Based Access Control) متقدم
- **Repository Pattern** - فصل منطق قاعدة البيانات عن منطق العمل
- **Services Layer** - طبقة خدمات للمنطق التجاري
- **تسجيل الأنشطة** - تتبع جميع أعمال المستخدمين
- **API Documentation** - وثائق تفاعلية باستخدام Swagger/OpenAPI

### 🔒 الأمان والحماية
- **JWT Authentication** مع انتهاء صلاحية مخصص
- **Rate Limiting** لمنع إساءة الاستخدام
- **CORS** مُكوّن للتطبيقات الأمامية
- **Security Headers** للحماية من الهجمات
- **Activity Logging** لتتبع جميع العمليات
- **Permission-based Access Control** دقيق

### 📊 إدارة البيانات
- **نماذج مخصصة** للمستخدمين والأدوار والصلاحيات
- **علاقات معقدة** مع تواريخ انتهاء وتتبع التعيين
- **Soft Delete** للحفاظ على البيانات التاريخية
- **Pagination** للقوائم الطويلة
- **Search & Filter** متقدم

## 🛠️ التقنيات المستخدمة

- **Django 5.2.6** - إطار العمل الأساسي
- **Django REST Framework 3.15.2** - لبناء APIs
- **djangorestframework-simplejwt 5.3.0** - للمصادقة بـ JWT
- **drf-spectacular 0.27.2** - لتوليد وثائق API
- **django-cors-headers 4.4.0** - لإدارة CORS
- **SQLite** - قاعدة البيانات (يمكن تغييرها بسهولة)

## 📁 هيكل المشروع

```
src/
├── config/                 # إعدادات Django
│   ├── settings.py         # إعدادات المشروع
│   └── urls.py            # URLs الرئيسية
├── accounts/              # تطبيق إدارة المستخدمين
│   ├── models.py          # نماذج قاعدة البيانات
│   ├── serializers.py     # Serializers للـ API
│   ├── views.py           # API Views
│   ├── repositories.py    # Repository Pattern
│   ├── services.py        # Services Layer
│   ├── permissions.py     # صلاحيات مخصصة
│   ├── middleware.py      # Middleware للأمان والتسجيل
│   ├── authentication.py  # مصادقة مخصصة
│   ├── urls.py           # URLs للـ API
│   └── tests.py          # اختبارات شاملة
└── requirements.txt       # المتطلبات
```

## 🚀 التثبيت والتشغيل

### 1. استنساخ المشروع
```bash
git clone <repository-url>
cd user-management-api
```

### 2. إنشاء البيئة الافتراضية
```bash
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# أو
venv\Scripts\activate     # Windows
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4. إعداد قاعدة البيانات
```bash
cd src
python manage.py makemigrations
python manage.py migrate
```

### 5. إنشاء البيانات التجريبية
```bash
python manage.py create_api_sample_data
```

### 6. تشغيل الخادم
```bash
python manage.py runserver
```

## 🔑 الحسابات التجريبية

بعد تشغيل `create_api_sample_data`، ستحصل على الحسابات التالية:

| المستخدم | كلمة المرور | الدور | الصلاحيات |
|----------|-------------|-------|-----------|
| `api_admin` | `admin123` | مدير عام API | جميع الصلاحيات |
| `api_user_manager` | `manager123` | مدير المستخدمين API | إدارة المستخدمين والأدوار |
| `api_editor` | `editor123` | محرر API | قراءة وكتابة (بدون حذف) |
| `api_monitor` | `monitor123` | مراقب API | قراءة فقط |
| `api_user` | `user123` | مستخدم عادي API | صلاحيات محدودة |

## 📚 API Endpoints

### 🔐 المصادقة
- `POST /api/v1/auth/login/` - تسجيل الدخول
- `POST /api/v1/auth/logout/` - تسجيل الخروج
- `POST /api/v1/auth/refresh/` - تجديد Token

### 👥 المستخدمين
- `GET /api/v1/users/` - قائمة المستخدمين
- `POST /api/v1/users/` - إنشاء مستخدم جديد
- `GET /api/v1/users/{id}/` - تفاصيل مستخدم
- `PUT/PATCH /api/v1/users/{id}/` - تحديث مستخدم
- `DELETE /api/v1/users/{id}/` - حذف مستخدم

### 🎭 الأدوار
- `GET /api/v1/roles/` - قائمة الأدوار
- `POST /api/v1/roles/` - إنشاء دور جديد
- `GET /api/v1/roles/{id}/` - تفاصيل دور
- `PUT/PATCH /api/v1/roles/{id}/` - تحديث دور
- `DELETE /api/v1/roles/{id}/` - حذف دور

### 🔑 الصلاحيات
- `GET /api/v1/permissions/` - قائمة الصلاحيات
- `POST /api/v1/permissions/` - إنشاء صلاحية جديدة
- `GET /api/v1/permissions/{id}/` - تفاصيل صلاحية

### 🔗 تعيين الأدوار والصلاحيات
- `POST /api/v1/assign-role/` - تعيين دور لمستخدم
- `POST /api/v1/remove-role/` - إزالة دور من مستخدم
- `POST /api/v1/assign-permission/` - تعيين صلاحية لدور
- `POST /api/v1/remove-permission/` - إزالة صلاحية من دور

### 👤 الملف الشخصي
- `GET /api/v1/profile/` - عرض الملف الشخصي
- `PUT /api/v1/profile/` - تحديث الملف الشخصي
- `POST /api/v1/change-password/` - تغيير كلمة المرور

### 📊 الأنشطة
- `GET /api/v1/activities/` - عرض سجل الأنشطة

## 📖 الوثائق التفاعلية

يمكنك الوصول للوثائق التفاعلية عبر:

- **Swagger UI**: `http://127.0.0.1:8000/api/docs/`
- **ReDoc**: `http://127.0.0.1:8000/api/redoc/`
- **OpenAPI Schema**: `http://127.0.0.1:8000/api/schema/`

## 🧪 تشغيل الاختبارات

```bash
# تشغيل جميع الاختبارات
python manage.py test accounts

# تشغيل اختبارات محددة
python manage.py test accounts.tests.UserAPITest

# تشغيل مع تفاصيل أكثر
python manage.py test accounts -v 2
```

## 🔧 أمثلة الاستخدام

### تسجيل الدخول
```bash
curl -X POST http://127.0.0.1:8000/api/v1/auth/login/ \
  -H "Content-Type: application/json" \
  -d '{
    "username": "api_admin",
    "password": "admin123"
  }'
```

### إنشاء مستخدم جديد
```bash
curl -X POST http://127.0.0.1:8000/api/v1/users/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "username": "new_user",
    "email": "<EMAIL>",
    "first_name": "New",
    "last_name": "User",
    "password": "newpass123",
    "confirm_password": "newpass123"
  }'
```

### تعيين دور لمستخدم
```bash
curl -X POST http://127.0.0.1:8000/api/v1/assign-role/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "user_id": 1,
    "role_id": 2
  }'
```

## 🔒 نظام الصلاحيات

النظام يدعم أنواع مختلفة من الصلاحيات:

- **read** - قراءة البيانات
- **write** - إنشاء وتعديل البيانات
- **delete** - حذف البيانات
- **manage** - إدارة كاملة (تعيين أدوار، إدارة صلاحيات)

## 🚀 النشر في الإنتاج

### إعدادات الإنتاج
1. تغيير `DEBUG = False` في settings.py
2. إعداد قاعدة بيانات إنتاج (PostgreSQL مُوصى به)
3. إعداد Redis للـ caching و rate limiting
4. استخدام خادم WSGI مثل Gunicorn
5. إعداد Nginx كـ reverse proxy
6. تفعيل HTTPS

### متغيرات البيئة
```bash
export SECRET_KEY="your-secret-key"
export DEBUG=False
export DATABASE_URL="postgresql://user:pass@localhost/dbname"
export REDIS_URL="redis://localhost:6379"
```

## 🤝 المساهمة

1. Fork المشروع
2. إنشاء branch جديد (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push للـ branch (`git push origin feature/amazing-feature`)
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم

إذا كان لديك أي أسئلة أو مشاكل، يرجى فتح issue في GitHub أو التواصل معنا.

---

**تم تطوير هذا النظام باستخدام أفضل الممارسات في تطوير APIs مع Django REST Framework**
