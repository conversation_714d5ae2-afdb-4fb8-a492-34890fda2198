# وثائق API - نظام إدارة المستخدمين

## 🔗 Base URL
```
http://127.0.0.1:8000/api/v1/
```

## 🔐 المصادقة

جميع endpoints (عدا تسجيل الدخول) تتطلب JWT token في header:
```
Authorization: Bearer YOUR_ACCESS_TOKEN
```

---

## 📋 Authentication Endpoints

### 1. تسجيل الدخول
**POST** `/auth/login/`

**Request Body:**
```json
{
  "username": "api_admin",
  "password": "admin123"
}
```

**Response (200):**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "Bearer",
  "user": {
    "id": 1,
    "username": "api_admin",
    "email": "<EMAIL>",
    "full_name": "مدير API",
    "roles": [{"id": 1, "name": "مدير عام API"}],
    "permissions": [...]
  }
}
```

### 2. تسجيل الخروج
**POST** `/auth/logout/`

**Headers:** `Authorization: Bearer TOKEN`

**Response (200):**
```json
{
  "message": "تم تسجيل الخروج بنجاح"
}
```

### 3. تجديد Token
**POST** `/auth/refresh/`

**Request Body:**
```json
{
  "refresh": "REFRESH_TOKEN"
}
```

**Response (200):**
```json
{
  "access": "NEW_ACCESS_TOKEN"
}
```

---

## 👥 User Management Endpoints

### 1. قائمة المستخدمين
**GET** `/users/`

**Query Parameters:**
- `search` (string): البحث في الاسم أو البريد الإلكتروني
- `is_active` (boolean): فلترة حسب الحالة
- `page` (int): رقم الصفحة
- `page_size` (int): عدد العناصر في الصفحة

**Response (200):**
```json
{
  "count": 5,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": 1,
      "username": "api_admin",
      "email": "<EMAIL>",
      "first_name": "مدير",
      "last_name": "API",
      "full_name": "مدير API",
      "is_active": true,
      "is_staff": true,
      "date_joined": "2025-09-08T23:45:00Z",
      "roles": [{"id": 1, "name": "مدير عام API"}],
      "permissions": [...]
    }
  ]
}
```

### 2. إنشاء مستخدم جديد
**POST** `/users/`

**Request Body:**
```json
{
  "username": "new_user",
  "email": "<EMAIL>",
  "first_name": "مستخدم",
  "last_name": "جديد",
  "password": "newpass123",
  "confirm_password": "newpass123",
  "phone_number": "+966501234567",
  "birth_date": "1990-01-01"
}
```

**Response (201):**
```json
{
  "id": 6,
  "username": "new_user",
  "email": "<EMAIL>",
  "first_name": "مستخدم",
  "last_name": "جديد",
  "full_name": "مستخدم جديد",
  "phone_number": "+966501234567",
  "birth_date": "1990-01-01",
  "is_active": true,
  "date_joined": "2025-09-08T23:50:00Z",
  "roles": [],
  "permissions": []
}
```

### 3. تفاصيل مستخدم
**GET** `/users/{id}/`

**Response (200):**
```json
{
  "id": 1,
  "username": "api_admin",
  "email": "<EMAIL>",
  "first_name": "مدير",
  "last_name": "API",
  "full_name": "مدير API",
  "phone_number": null,
  "birth_date": null,
  "profile_picture": null,
  "is_active": true,
  "is_staff": true,
  "is_superuser": true,
  "date_joined": "2025-09-08T23:45:00Z",
  "last_login": "2025-09-08T23:48:00Z",
  "roles": [{"id": 1, "name": "مدير عام API"}],
  "permissions": [...]
}
```

### 4. تحديث مستخدم
**PUT/PATCH** `/users/{id}/`

**Request Body (PATCH):**
```json
{
  "first_name": "اسم محدث",
  "email": "<EMAIL>"
}
```

**Response (200):**
```json
{
  "id": 1,
  "username": "api_admin",
  "email": "<EMAIL>",
  "first_name": "اسم محدث",
  "last_name": "API",
  "full_name": "اسم محدث API",
  ...
}
```

### 5. حذف مستخدم
**DELETE** `/users/{id}/`

**Response (204):** No Content

---

## 🎭 Role Management Endpoints

### 1. قائمة الأدوار
**GET** `/roles/`

**Query Parameters:**
- `search` (string): البحث في اسم أو وصف الدور
- `is_active` (boolean): فلترة حسب الحالة

**Response (200):**
```json
[
  {
    "id": 1,
    "name": "مدير عام API",
    "description": "مدير عام للنظام مع جميع صلاحيات API",
    "is_active": true,
    "created_at": "2025-09-08T23:45:00Z",
    "updated_at": "2025-09-08T23:45:00Z",
    "permissions": [
      {
        "id": 1,
        "name": "قراءة المستخدمين",
        "type": "read"
      }
    ],
    "users_count": 1,
    "permissions_count": 16
  }
]
```

### 2. إنشاء دور جديد
**POST** `/roles/`

**Request Body:**
```json
{
  "name": "دور جديد",
  "description": "وصف الدور الجديد",
  "is_active": true
}
```

**Response (201):**
```json
{
  "id": 6,
  "name": "دور جديد",
  "description": "وصف الدور الجديد",
  "is_active": true,
  "created_at": "2025-09-08T23:55:00Z",
  "updated_at": "2025-09-08T23:55:00Z",
  "permissions": [],
  "users_count": 0,
  "permissions_count": 0
}
```

---

## 🔑 Permission Management Endpoints

### 1. قائمة الصلاحيات
**GET** `/permissions/`

**Query Parameters:**
- `search` (string): البحث في اسم أو وصف الصلاحية
- `permission_type` (string): فلترة حسب النوع (read, write, delete, manage)

**Response (200):**
```json
[
  {
    "id": 1,
    "name": "قراءة المستخدمين",
    "description": "عرض قائمة المستخدمين عبر API",
    "permission_type": "read",
    "is_active": true,
    "created_at": "2025-09-08T23:45:00Z",
    "updated_at": "2025-09-08T23:45:00Z",
    "roles_count": 3
  }
]
```

### 2. إنشاء صلاحية جديدة
**POST** `/permissions/`

**Request Body:**
```json
{
  "name": "صلاحية جديدة",
  "description": "وصف الصلاحية الجديدة",
  "permission_type": "write",
  "is_active": true
}
```

---

## 🔗 Role & Permission Assignment

### 1. تعيين دور لمستخدم
**POST** `/assign-role/`

**Request Body:**
```json
{
  "user_id": 6,
  "role_id": 2,
  "expires_at": "2025-12-31T23:59:59Z"  // اختياري
}
```

**Response (200):**
```json
{
  "message": "تم تعيين الدور بنجاح"
}
```

### 2. إزالة دور من مستخدم
**POST** `/remove-role/`

**Request Body:**
```json
{
  "user_id": 6,
  "role_id": 2
}
```

**Response (200):**
```json
{
  "message": "تم إزالة الدور بنجاح"
}
```

### 3. تعيين صلاحية لدور
**POST** `/assign-permission/`

**Request Body:**
```json
{
  "role_id": 6,
  "permission_id": 1
}
```

**Response (200):**
```json
{
  "message": "تم تعيين الصلاحية بنجاح"
}
```

### 4. إزالة صلاحية من دور
**POST** `/remove-permission/`

**Request Body:**
```json
{
  "role_id": 6,
  "permission_id": 1
}
```

**Response (200):**
```json
{
  "message": "تم إزالة الصلاحية بنجاح"
}
```

---

## 👤 Profile Management

### 1. عرض الملف الشخصي
**GET** `/profile/`

**Response (200):**
```json
{
  "id": 1,
  "username": "api_admin",
  "email": "<EMAIL>",
  "first_name": "مدير",
  "last_name": "API",
  "full_name": "مدير API",
  "phone_number": null,
  "birth_date": null,
  "profile_picture": null,
  "date_joined": "2025-09-08T23:45:00Z",
  "last_login": "2025-09-08T23:48:00Z",
  "roles": ["مدير عام API"],
  "permissions": ["قراءة المستخدمين", "إضافة مستخدمين", ...]
}
```

### 2. تحديث الملف الشخصي
**PUT** `/profile/`

**Request Body:**
```json
{
  "first_name": "اسم محدث",
  "last_name": "عائلة محدثة",
  "email": "<EMAIL>",
  "phone_number": "+966501234567",
  "birth_date": "1985-05-15"
}
```

### 3. تغيير كلمة المرور
**POST** `/change-password/`

**Request Body:**
```json
{
  "old_password": "admin123",
  "new_password": "newpass123",
  "confirm_password": "newpass123"
}
```

**Response (200):**
```json
{
  "message": "تم تغيير كلمة المرور بنجاح"
}
```

---

## 📊 Activity Logs

### 1. عرض سجل الأنشطة
**GET** `/activities/`

**Query Parameters:**
- `user_id` (int): فلترة حسب المستخدم
- `action` (string): فلترة حسب نوع النشاط
- `page` (int): رقم الصفحة

**Response (200):**
```json
{
  "count": 25,
  "next": "http://127.0.0.1:8000/api/v1/activities/?page=2",
  "previous": null,
  "results": [
    {
      "id": 1,
      "user": 1,
      "user_name": "api_admin",
      "action": "login",
      "action_display": "تسجيل دخول",
      "description": "تسجيل دخول المستخدم api_admin",
      "ip_address": "127.0.0.1",
      "user_agent": "Mozilla/5.0...",
      "performed_by": 1,
      "performed_by_name": "api_admin",
      "timestamp": "2025-09-08T23:48:00Z"
    }
  ]
}
```

---

## ❌ Error Responses

### 400 Bad Request
```json
{
  "error": "بيانات غير صحيحة",
  "details": {
    "username": ["هذا الحقل مطلوب."],
    "email": ["أدخل عنوان بريد إلكتروني صحيح."]
  }
}
```

### 401 Unauthorized
```json
{
  "detail": "Authentication credentials were not provided."
}
```

### 403 Forbidden
```json
{
  "detail": "You do not have permission to perform this action."
}
```

### 404 Not Found
```json
{
  "detail": "Not found."
}
```

### 429 Too Many Requests
```json
{
  "error": "تم تجاوز الحد المسموح من الطلبات"
}
```

---

## 🔧 Rate Limiting

- **100 requests per minute** لكل IP address
- ينطبق على جميع API endpoints
- يتم إعادة تعيين العداد كل دقيقة

## 📝 Notes

1. جميع التواريخ بصيغة ISO 8601 UTC
2. الـ pagination يستخدم page-based pagination
3. البحث case-insensitive ويدعم البحث الجزئي
4. جميع الاستجابات بصيغة JSON
5. يتم تسجيل جميع العمليات في سجل الأنشطة
