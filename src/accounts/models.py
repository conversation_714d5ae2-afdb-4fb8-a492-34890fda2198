from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils import timezone
from datetime import timedelta

class User(AbstractUser):
    """نموذج المستخدم المخصص"""
    phone_number = models.CharField(max_length=20, blank=True, null=True, verbose_name="رقم الهاتف")
    birth_date = models.DateField(blank=True, null=True, verbose_name="تاريخ الميلاد")
    profile_picture = models.ImageField(upload_to='profiles/', blank=True, null=True, verbose_name="صورة الملف الشخصي")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "مستخدم"
        verbose_name_plural = "المستخدمون"
        db_table = 'accounts_user'

    def __str__(self):
        return self.username

    def get_full_name(self):
        """إرجاع الاسم الكامل للمستخدم"""
        return f"{self.first_name} {self.last_name}".strip() or self.username

    def has_role(self, role_name):
        """التحقق من وجود دور معين للمستخدم"""
        return self.user_roles.filter(
            role__name=role_name,
            is_active=True
        ).exists()

    def get_user_roles(self):
        """إرجاع جميع أدوار المستخدم النشطة"""
        return self.user_roles.filter(is_active=True).select_related('role')

    def get_all_permissions(self):
        """إرجاع جميع صلاحيات المستخدم من خلال الأدوار"""
        from .models import Permission
        role_ids = self.user_roles.filter(is_active=True).values_list('role_id', flat=True)
        permission_ids = RolePermission.objects.filter(
            role_id__in=role_ids,
            is_active=True
        ).values_list('permission_id', flat=True)
        return Permission.objects.filter(id__in=permission_ids, is_active=True)

class Role(models.Model):
    """نموذج الأدوار"""
    name = models.CharField(max_length=100, unique=True, verbose_name="اسم الدور")
    description = models.TextField(blank=True, null=True, verbose_name="الوصف")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "دور"
        verbose_name_plural = "الأدوار"
        db_table = 'accounts_role'

    def __str__(self):
        return self.name

    def get_permissions(self):
        """إرجاع جميع صلاحيات الدور"""
        return self.role_permissions.filter(is_active=True).select_related('permission')

    def add_permission(self, permission):
        """إضافة صلاحية للدور"""
        role_permission, created = RolePermission.objects.get_or_create(
            role=self,
            permission=permission,
            defaults={'is_active': True}
        )
        if not created and not role_permission.is_active:
            role_permission.is_active = True
            role_permission.save()
        return role_permission

    def remove_permission(self, permission):
        """إزالة صلاحية من الدور"""
        try:
            role_permission = RolePermission.objects.get(role=self, permission=permission)
            role_permission.is_active = False
            role_permission.save()
        except RolePermission.DoesNotExist:
            pass

class Permission(models.Model):
    """نموذج الصلاحيات"""
    PERMISSION_TYPES = [
        ('read', 'قراءة'),
        ('write', 'كتابة'),
        ('delete', 'حذف'),
        ('manage', 'إدارة'),
    ]

    name = models.CharField(max_length=100, unique=True, verbose_name="اسم الصلاحية")
    description = models.TextField(blank=True, null=True, verbose_name="الوصف")
    permission_type = models.CharField(max_length=20, choices=PERMISSION_TYPES, default='read', verbose_name="نوع الصلاحية")
    is_active = models.BooleanField(default=True, verbose_name="نشط")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ الإنشاء")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="تاريخ التحديث")

    class Meta:
        verbose_name = "صلاحية"
        verbose_name_plural = "الصلاحيات"
        db_table = 'accounts_permission'

    def __str__(self):
        return self.name

class UserRole(models.Model):
    """نموذج ربط المستخدمين بالأدوار"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='user_roles', verbose_name="المستخدم")
    role = models.ForeignKey(Role, on_delete=models.CASCADE, related_name='role_users', verbose_name="الدور")
    assigned_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ التعيين")
    assigned_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_roles', verbose_name="تم التعيين بواسطة")
    expires_at = models.DateTimeField(blank=True, null=True, verbose_name="تاريخ الانتهاء")
    is_active = models.BooleanField(default=True, verbose_name="نشط")

    class Meta:
        verbose_name = "دور المستخدم"
        verbose_name_plural = "أدوار المستخدمين"
        unique_together = ['user', 'role']
        db_table = 'accounts_user_role'

    def __str__(self):
        return f"{self.user.username} - {self.role.name}"

    def is_expired(self):
        """التحقق من انتهاء صلاحية الدور"""
        if self.expires_at:
            return timezone.now() > self.expires_at
        return False

class RolePermission(models.Model):
    """نموذج ربط الأدوار بالصلاحيات"""
    role = models.ForeignKey(Role, on_delete=models.CASCADE, related_name='role_permissions', verbose_name="الدور")
    permission = models.ForeignKey(Permission, on_delete=models.CASCADE, related_name='permission_roles', verbose_name="الصلاحية")
    assigned_at = models.DateTimeField(auto_now_add=True, verbose_name="تاريخ التعيين")
    assigned_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_permissions', verbose_name="تم التعيين بواسطة")
    is_active = models.BooleanField(default=True, verbose_name="نشط")

    class Meta:
        verbose_name = "صلاحية الدور"
        verbose_name_plural = "صلاحيات الأدوار"
        unique_together = ['role', 'permission']
        db_table = 'accounts_role_permission'

    def __str__(self):
        return f"{self.role.name} - {self.permission.name}"

class UserActivity(models.Model):
    """نموذج تسجيل أنشطة المستخدمين"""
    ACTION_CHOICES = [
        ('login', 'تسجيل دخول'),
        ('logout', 'تسجيل خروج'),
        ('role_assigned', 'تعيين دور'),
        ('role_removed', 'إزالة دور'),
        ('permission_granted', 'منح صلاحية'),
        ('permission_revoked', 'إلغاء صلاحية'),
        ('profile_updated', 'تحديث الملف الشخصي'),
        ('password_changed', 'تغيير كلمة المرور'),
        ('other', 'أخرى'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='activities', verbose_name="المستخدم")
    action = models.CharField(max_length=50, choices=ACTION_CHOICES, verbose_name="النشاط")
    description = models.TextField(verbose_name="الوصف")
    ip_address = models.GenericIPAddressField(blank=True, null=True, verbose_name="عنوان IP")
    user_agent = models.TextField(blank=True, null=True, verbose_name="معلومات المتصفح")
    performed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='performed_activities', verbose_name="تم بواسطة")
    timestamp = models.DateTimeField(auto_now_add=True, verbose_name="الوقت")

    class Meta:
        verbose_name = "نشاط المستخدم"
        verbose_name_plural = "أنشطة المستخدمين"
        ordering = ['-timestamp']
        db_table = 'accounts_user_activity'

    def __str__(self):
        return f"{self.user.username} - {self.get_action_display()} - {self.timestamp}"