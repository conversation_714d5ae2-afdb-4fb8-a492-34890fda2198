from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from .models import Role, Permission, UserRole, RolePermission, UserActivity
from .services import UserService, RoleService, PermissionService
from .repositories import UserRepository, RoleRepository, PermissionRepository

User = get_user_model()

class UserModelTest(TestCase):
    """اختبارات نموذج المستخدم"""

    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User'
        )

        self.role = Role.objects.create(
            name='Test Role',
            description='Test role description'
        )

        self.permission = Permission.objects.create(
            name='Test Permission',
            description='Test permission description',
            permission_type='read'
        )

    def test_user_creation(self):
        """اختبار إنشاء المستخدم"""
        self.assertEqual(self.user.username, 'testuser')
        self.assertEqual(self.user.email, '<EMAIL>')
        self.assertTrue(self.user.check_password('testpass123'))
        self.assertEqual(self.user.get_full_name(), 'Test User')

    def test_user_has_role(self):
        """اختبار تحقق من وجود دور للمستخدم"""
        # تعيين دور للمستخدم
        UserRole.objects.create(user=self.user, role=self.role)

        self.assertTrue(self.user.has_role(self.role.name))
        self.assertFalse(self.user.has_role('Non-existent Role'))

    def test_user_permissions(self):
        """اختبار صلاحيات المستخدم"""
        # تعيين صلاحية للدور
        RolePermission.objects.create(role=self.role, permission=self.permission)

        # تعيين دور للمستخدم
        UserRole.objects.create(user=self.user, role=self.role)

        permissions = self.user.get_all_permissions()
        self.assertIn(self.permission, permissions)

class RepositoryTest(TestCase):
    """اختبارات Repository Pattern"""

    def setUp(self):
        self.user_repo = UserRepository()
        self.role_repo = RoleRepository()
        self.permission_repo = PermissionRepository()

    def test_user_repository_crud(self):
        """اختبار عمليات CRUD للمستخدمين"""
        # إنشاء
        user_data = {
            'username': 'repo_test',
            'email': '<EMAIL>',
            'first_name': 'Repo',
            'last_name': 'Test'
        }
        user = self.user_repo.create(**user_data)
        self.assertIsNotNone(user)
        self.assertEqual(user.username, 'repo_test')

        # قراءة
        retrieved_user = self.user_repo.get_by_id(user.id)
        self.assertEqual(retrieved_user.id, user.id)

        # تحديث
        updated_user = self.user_repo.update(user.id, first_name='Updated')
        self.assertEqual(updated_user.first_name, 'Updated')

        # حذف
        success = self.user_repo.delete(user.id)
        self.assertTrue(success)
        self.assertIsNone(self.user_repo.get_by_id(user.id))

    def test_role_repository_crud(self):
        """اختبار عمليات CRUD للأدوار"""
        # إنشاء
        role_data = {
            'name': 'Test Role',
            'description': 'Test role description'
        }
        role = self.role_repo.create(**role_data)
        self.assertIsNotNone(role)
        self.assertEqual(role.name, 'Test Role')

        # قراءة
        retrieved_role = self.role_repo.get_by_id(role.id)
        self.assertEqual(retrieved_role.id, role.id)

        # تحديث
        updated_role = self.role_repo.update(role.id, description='Updated description')
        self.assertEqual(updated_role.description, 'Updated description')

        # حذف
        success = self.role_repo.delete(role.id)
        self.assertTrue(success)

class ServiceTest(TestCase):
    """اختبارات Services"""

    def setUp(self):
        self.user_service = UserService()
        self.role_service = RoleService()
        self.permission_service = PermissionService()

        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            is_staff=True,
            is_superuser=True
        )

    def test_user_service_create_user(self):
        """اختبار إنشاء مستخدم عبر Service"""
        user_data = {
            'username': 'service_test',
            'email': '<EMAIL>',
            'first_name': 'Service',
            'last_name': 'Test',
            'password': 'testpass123'
        }

        user = self.user_service.create_user(user_data, self.admin_user.id)
        self.assertIsNotNone(user)
        self.assertEqual(user.username, 'service_test')

        # التحقق من تسجيل النشاط
        activity = UserActivity.objects.filter(user=user, action='profile_updated').first()
        self.assertIsNotNone(activity)

    def test_user_authentication(self):
        """اختبار مصادقة المستخدم"""
        user = User.objects.create_user(
            username='auth_test',
            password='testpass123'
        )

        # مصادقة صحيحة
        auth_data = self.user_service.authenticate_user('auth_test', 'testpass123')
        self.assertIsNotNone(auth_data)
        self.assertIn('access_token', auth_data)
        self.assertIn('refresh_token', auth_data)

        # مصادقة خاطئة
        auth_data = self.user_service.authenticate_user('auth_test', 'wrongpass')
        self.assertIsNone(auth_data)

    def test_role_assignment(self):
        """اختبار تعيين الأدوار"""
        user = User.objects.create_user(username='role_test', password='test123')
        role = Role.objects.create(name='Test Role')

        success = self.user_service.assign_role_to_user(
            user.id, role.id, self.admin_user.id
        )
        self.assertTrue(success)

        # التحقق من تعيين الدور
        user_role = UserRole.objects.filter(user=user, role=role).first()
        self.assertIsNotNone(user_role)
        self.assertTrue(user_role.is_active)

class APIAuthenticationTest(APITestCase):
    """اختبارات مصادقة API"""

    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(
            username='api_test',
            email='<EMAIL>',
            password='testpass123'
        )

    def test_login_api(self):
        """اختبار تسجيل الدخول عبر API"""
        url = reverse('accounts:login')
        data = {
            'username': 'api_test',
            'password': 'testpass123'
        }

        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('access_token', response.data)
        self.assertIn('refresh_token', response.data)
        self.assertIn('user', response.data)

    def test_login_invalid_credentials(self):
        """اختبار تسجيل الدخول ببيانات خاطئة"""
        url = reverse('accounts:login')
        data = {
            'username': 'api_test',
            'password': 'wrongpass'
        }

        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_logout_api(self):
        """اختبار تسجيل الخروج عبر API"""
        # تسجيل الدخول أولاً
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')

        url = reverse('accounts:logout')
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_protected_endpoint_without_token(self):
        """اختبار الوصول لـ endpoint محمي بدون token"""
        url = reverse('accounts:profile')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_protected_endpoint_with_token(self):
        """اختبار الوصول لـ endpoint محمي مع token"""
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')

        url = reverse('accounts:profile')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['username'], self.user.username)

class UserAPITest(APITestCase):
    """اختبارات API المستخدمين"""

    def setUp(self):
        self.client = APIClient()

        # إنشاء مدير عام
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            is_staff=True,
            is_superuser=True
        )

        # إنشاء مستخدم عادي
        self.normal_user = User.objects.create_user(
            username='normal',
            email='<EMAIL>',
            password='normal123'
        )

        # إنشاء دور وصلاحيات
        self.role = Role.objects.create(name='Test Role')
        self.permission = Permission.objects.create(
            name='قراءة المستخدمين',
            permission_type='read'
        )
        RolePermission.objects.create(role=self.role, permission=self.permission)

    def authenticate_as_admin(self):
        """مصادقة كمدير عام"""
        refresh = RefreshToken.for_user(self.admin_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')

    def authenticate_as_normal_user(self):
        """مصادقة كمستخدم عادي"""
        refresh = RefreshToken.for_user(self.normal_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')

    def test_list_users_as_admin(self):
        """اختبار عرض قائمة المستخدمين كمدير"""
        self.authenticate_as_admin()

        url = reverse('accounts:user-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreaterEqual(len(response.data), 2)  # admin + normal user

    def test_list_users_as_normal_user(self):
        """اختبار عرض قائمة المستخدمين كمستخدم عادي"""
        self.authenticate_as_normal_user()

        url = reverse('accounts:user-list')
        response = self.client.get(url)
        # يجب أن يكون ممنوع للمستخدم العادي
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_create_user_as_admin(self):
        """اختبار إنشاء مستخدم كمدير"""
        self.authenticate_as_admin()

        url = reverse('accounts:user-list')
        data = {
            'username': 'new_user',
            'email': '<EMAIL>',
            'first_name': 'New',
            'last_name': 'User',
            'password': 'newpass123',
            'confirm_password': 'newpass123'
        }

        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['username'], 'new_user')

        # التحقق من إنشاء المستخدم في قاعدة البيانات
        user = User.objects.get(username='new_user')
        self.assertIsNotNone(user)

    def test_update_user_as_admin(self):
        """اختبار تحديث مستخدم كمدير"""
        self.authenticate_as_admin()

        url = reverse('accounts:user-detail', kwargs={'pk': self.normal_user.id})
        data = {
            'first_name': 'Updated Name'
        }

        response = self.client.patch(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['first_name'], 'Updated Name')

    def test_delete_user_as_admin(self):
        """اختبار حذف مستخدم كمدير"""
        self.authenticate_as_admin()

        # إنشاء مستخدم للحذف
        user_to_delete = User.objects.create_user(
            username='to_delete',
            email='<EMAIL>',
            password='delete123'
        )

        url = reverse('accounts:user-detail', kwargs={'pk': user_to_delete.id})
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # التحقق من حذف المستخدم
        self.assertFalse(User.objects.filter(id=user_to_delete.id).exists())

class RoleAPITest(APITestCase):
    """اختبارات API الأدوار"""

    def setUp(self):
        self.client = APIClient()

        # إنشاء مدير عام
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            is_staff=True,
            is_superuser=True
        )

        # إنشاء دور للاختبار
        self.test_role = Role.objects.create(
            name='Test Role',
            description='Test role for API testing'
        )

    def authenticate_as_admin(self):
        """مصادقة كمدير عام"""
        refresh = RefreshToken.for_user(self.admin_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')

    def test_list_roles(self):
        """اختبار عرض قائمة الأدوار"""
        self.authenticate_as_admin()

        url = reverse('accounts:role-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreaterEqual(len(response.data), 1)

    def test_create_role(self):
        """اختبار إنشاء دور جديد"""
        self.authenticate_as_admin()

        url = reverse('accounts:role-list')
        data = {
            'name': 'New Role',
            'description': 'New role description',
            'is_active': True
        }

        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['name'], 'New Role')

        # التحقق من إنشاء الدور في قاعدة البيانات
        role = Role.objects.get(name='New Role')
        self.assertIsNotNone(role)

    def test_update_role(self):
        """اختبار تحديث دور"""
        self.authenticate_as_admin()

        url = reverse('accounts:role-detail', kwargs={'pk': self.test_role.id})
        data = {
            'description': 'Updated description'
        }

        response = self.client.patch(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['description'], 'Updated description')

    def test_delete_role(self):
        """اختبار حذف دور"""
        self.authenticate_as_admin()

        # إنشاء دور للحذف
        role_to_delete = Role.objects.create(
            name='Role to Delete',
            description='This role will be deleted'
        )

        url = reverse('accounts:role-detail', kwargs={'pk': role_to_delete.id})
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)

        # التحقق من حذف الدور
        self.assertFalse(Role.objects.filter(id=role_to_delete.id).exists())

class RoleAssignmentAPITest(APITestCase):
    """اختبارات API تعيين الأدوار"""

    def setUp(self):
        self.client = APIClient()

        # إنشاء مدير عام
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            is_staff=True,
            is_superuser=True
        )

        # إنشاء مستخدم للاختبار
        self.test_user = User.objects.create_user(
            username='test_user',
            email='<EMAIL>',
            password='test123'
        )

        # إنشاء دور للاختبار
        self.test_role = Role.objects.create(
            name='Test Role',
            description='Test role for assignment'
        )

    def authenticate_as_admin(self):
        """مصادقة كمدير عام"""
        refresh = RefreshToken.for_user(self.admin_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')

    def test_assign_role_to_user(self):
        """اختبار تعيين دور لمستخدم"""
        self.authenticate_as_admin()

        url = reverse('accounts:assign_role')
        data = {
            'user_id': self.test_user.id,
            'role_id': self.test_role.id
        }

        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # التحقق من تعيين الدور
        user_role = UserRole.objects.filter(
            user=self.test_user,
            role=self.test_role,
            is_active=True
        ).first()
        self.assertIsNotNone(user_role)

    def test_remove_role_from_user(self):
        """اختبار إزالة دور من مستخدم"""
        self.authenticate_as_admin()

        # تعيين دور أولاً
        UserRole.objects.create(
            user=self.test_user,
            role=self.test_role,
            assigned_by=self.admin_user
        )

        url = reverse('accounts:remove_role')
        data = {
            'user_id': self.test_user.id,
            'role_id': self.test_role.id
        }

        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # التحقق من إزالة الدور
        user_role = UserRole.objects.filter(
            user=self.test_user,
            role=self.test_role,
            is_active=True
        ).first()
        self.assertIsNone(user_role)

class PermissionAPITest(APITestCase):
    """اختبارات API الصلاحيات"""

    def setUp(self):
        self.client = APIClient()

        # إنشاء مدير عام
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            is_staff=True,
            is_superuser=True
        )

        # إنشاء صلاحية للاختبار
        self.test_permission = Permission.objects.create(
            name='Test Permission',
            description='Test permission for API testing',
            permission_type='read'
        )

    def authenticate_as_admin(self):
        """مصادقة كمدير عام"""
        refresh = RefreshToken.for_user(self.admin_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')

    def test_list_permissions(self):
        """اختبار عرض قائمة الصلاحيات"""
        self.authenticate_as_admin()

        url = reverse('accounts:permission-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreaterEqual(len(response.data), 1)

    def test_create_permission(self):
        """اختبار إنشاء صلاحية جديدة"""
        self.authenticate_as_admin()

        url = reverse('accounts:permission-list')
        data = {
            'name': 'New Permission',
            'description': 'New permission description',
            'permission_type': 'write',
            'is_active': True
        }

        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['name'], 'New Permission')

        # التحقق من إنشاء الصلاحية في قاعدة البيانات
        permission = Permission.objects.get(name='New Permission')
        self.assertIsNotNone(permission)

class ActivityAPITest(APITestCase):
    """اختبارات API الأنشطة"""

    def setUp(self):
        self.client = APIClient()

        # إنشاء مدير عام
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='admin123',
            is_staff=True,
            is_superuser=True
        )

        # إنشاء نشاط للاختبار
        UserActivity.objects.create(
            user=self.admin_user,
            action='login',
            description='Test login activity',
            performed_by=self.admin_user
        )

    def authenticate_as_admin(self):
        """مصادقة كمدير عام"""
        refresh = RefreshToken.for_user(self.admin_user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')

    def test_list_activities(self):
        """اختبار عرض قائمة الأنشطة"""
        self.authenticate_as_admin()

        url = reverse('accounts:activities')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertGreaterEqual(len(response.data['results']), 1)

class ProfileAPITest(APITestCase):
    """اختبارات API الملف الشخصي"""

    def setUp(self):
        self.client = APIClient()

        self.user = User.objects.create_user(
            username='profile_test',
            email='<EMAIL>',
            password='test123',
            first_name='Profile',
            last_name='Test'
        )

    def authenticate_as_user(self):
        """مصادقة كمستخدم"""
        refresh = RefreshToken.for_user(self.user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')

    def test_get_profile(self):
        """اختبار عرض الملف الشخصي"""
        self.authenticate_as_user()

        url = reverse('accounts:profile')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['username'], self.user.username)

    def test_update_profile(self):
        """اختبار تحديث الملف الشخصي"""
        self.authenticate_as_user()

        url = reverse('accounts:profile')
        data = {
            'first_name': 'Updated Profile'
        }

        response = self.client.put(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['first_name'], 'Updated Profile')

    def test_change_password(self):
        """اختبار تغيير كلمة المرور"""
        self.authenticate_as_user()

        url = reverse('accounts:change_password')
        data = {
            'old_password': 'test123',
            'new_password': 'newpass123',
            'confirm_password': 'newpass123'
        }

        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # التحقق من تغيير كلمة المرور
        self.user.refresh_from_db()
        self.assertTrue(self.user.check_password('newpass123'))
