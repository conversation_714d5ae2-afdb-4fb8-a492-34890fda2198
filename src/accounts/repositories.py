from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from django.db.models import QuerySet, Q
from django.contrib.auth import get_user_model
from .models import Role, Permission, UserRole, RolePermission, UserActivity

User = get_user_model()

class BaseRepository(ABC):
    """Repository أساسي مجرد"""
    
    @abstractmethod
    def get_all(self) -> QuerySet:
        pass
    
    @abstractmethod
    def get_by_id(self, id: int) -> Optional[Any]:
        pass
    
    @abstractmethod
    def create(self, **kwargs) -> Any:
        pass
    
    @abstractmethod
    def update(self, id: int, **kwargs) -> Optional[Any]:
        pass
    
    @abstractmethod
    def delete(self, id: int) -> bool:
        pass

class UserRepository(BaseRepository):
    """Repository للمستخدمين"""
    
    def get_all(self) -> QuerySet:
        """إرجاع جميع المستخدمين"""
        return User.objects.all().order_by('-date_joined')
    
    def get_by_id(self, id: int) -> Optional[User]:
        """إرجاع مستخدم بالمعرف"""
        try:
            return User.objects.get(id=id)
        except User.DoesNotExist:
            return None
    
    def get_by_username(self, username: str) -> Optional[User]:
        """إرجاع مستخدم بالاسم"""
        try:
            return User.objects.get(username=username)
        except User.DoesNotExist:
            return None
    
    def get_by_email(self, email: str) -> Optional[User]:
        """إرجاع مستخدم بالبريد الإلكتروني"""
        try:
            return User.objects.get(email=email)
        except User.DoesNotExist:
            return None
    
    def create(self, **kwargs) -> User:
        """إنشاء مستخدم جديد"""
        password = kwargs.pop('password', None)
        user = User.objects.create_user(**kwargs)
        if password:
            user.set_password(password)
            user.save()
        return user
    
    def update(self, id: int, **kwargs) -> Optional[User]:
        """تحديث مستخدم"""
        user = self.get_by_id(id)
        if user:
            for key, value in kwargs.items():
                if key == 'password':
                    user.set_password(value)
                else:
                    setattr(user, key, value)
            user.save()
        return user
    
    def delete(self, id: int) -> bool:
        """حذف مستخدم"""
        user = self.get_by_id(id)
        if user:
            user.delete()
            return True
        return False
    
    def search(self, query: str) -> QuerySet:
        """البحث في المستخدمين"""
        return User.objects.filter(
            Q(username__icontains=query) |
            Q(first_name__icontains=query) |
            Q(last_name__icontains=query) |
            Q(email__icontains=query)
        ).order_by('-date_joined')
    
    def get_active_users(self) -> QuerySet:
        """إرجاع المستخدمين النشطين فقط"""
        return User.objects.filter(is_active=True).order_by('-date_joined')
    
    def get_users_with_role(self, role_name: str) -> QuerySet:
        """إرجاع المستخدمين الذين لديهم دور معين"""
        return User.objects.filter(
            user_roles__role__name=role_name,
            user_roles__is_active=True
        ).distinct()
    
    def get_user_roles(self, user_id: int) -> QuerySet:
        """إرجاع أدوار مستخدم معين"""
        return UserRole.objects.filter(
            user_id=user_id,
            is_active=True
        ).select_related('role')
    
    def get_user_permissions(self, user_id: int) -> QuerySet:
        """إرجاع صلاحيات مستخدم معين"""
        user = self.get_by_id(user_id)
        if user:
            return user.get_all_permissions()
        return Permission.objects.none()

class RoleRepository(BaseRepository):
    """Repository للأدوار"""
    
    def get_all(self) -> QuerySet:
        """إرجاع جميع الأدوار"""
        return Role.objects.all().order_by('name')
    
    def get_by_id(self, id: int) -> Optional[Role]:
        """إرجاع دور بالمعرف"""
        try:
            return Role.objects.get(id=id)
        except Role.DoesNotExist:
            return None
    
    def get_by_name(self, name: str) -> Optional[Role]:
        """إرجاع دور بالاسم"""
        try:
            return Role.objects.get(name=name)
        except Role.DoesNotExist:
            return None
    
    def create(self, **kwargs) -> Role:
        """إنشاء دور جديد"""
        return Role.objects.create(**kwargs)
    
    def update(self, id: int, **kwargs) -> Optional[Role]:
        """تحديث دور"""
        role = self.get_by_id(id)
        if role:
            for key, value in kwargs.items():
                setattr(role, key, value)
            role.save()
        return role
    
    def delete(self, id: int) -> bool:
        """حذف دور"""
        role = self.get_by_id(id)
        if role:
            role.delete()
            return True
        return False
    
    def search(self, query: str) -> QuerySet:
        """البحث في الأدوار"""
        return Role.objects.filter(
            Q(name__icontains=query) |
            Q(description__icontains=query)
        ).order_by('name')
    
    def get_active_roles(self) -> QuerySet:
        """إرجاع الأدوار النشطة فقط"""
        return Role.objects.filter(is_active=True).order_by('name')
    
    def get_role_permissions(self, role_id: int) -> QuerySet:
        """إرجاع صلاحيات دور معين"""
        return RolePermission.objects.filter(
            role_id=role_id,
            is_active=True
        ).select_related('permission')
    
    def get_role_users(self, role_id: int) -> QuerySet:
        """إرجاع مستخدمي دور معين"""
        return UserRole.objects.filter(
            role_id=role_id,
            is_active=True
        ).select_related('user')
    
    def assign_permission(self, role_id: int, permission_id: int, assigned_by_id: int = None) -> Optional[RolePermission]:
        """تعيين صلاحية لدور"""
        role = self.get_by_id(role_id)
        if role:
            permission = PermissionRepository().get_by_id(permission_id)
            if permission:
                role_permission, created = RolePermission.objects.get_or_create(
                    role=role,
                    permission=permission,
                    defaults={
                        'assigned_by_id': assigned_by_id,
                        'is_active': True
                    }
                )
                if not created and not role_permission.is_active:
                    role_permission.is_active = True
                    role_permission.assigned_by_id = assigned_by_id
                    role_permission.save()
                return role_permission
        return None
    
    def remove_permission(self, role_id: int, permission_id: int) -> bool:
        """إزالة صلاحية من دور"""
        try:
            role_permission = RolePermission.objects.get(
                role_id=role_id,
                permission_id=permission_id
            )
            role_permission.is_active = False
            role_permission.save()
            return True
        except RolePermission.DoesNotExist:
            return False

class PermissionRepository(BaseRepository):
    """Repository للصلاحيات"""
    
    def get_all(self) -> QuerySet:
        """إرجاع جميع الصلاحيات"""
        return Permission.objects.all().order_by('name')
    
    def get_by_id(self, id: int) -> Optional[Permission]:
        """إرجاع صلاحية بالمعرف"""
        try:
            return Permission.objects.get(id=id)
        except Permission.DoesNotExist:
            return None
    
    def get_by_name(self, name: str) -> Optional[Permission]:
        """إرجاع صلاحية بالاسم"""
        try:
            return Permission.objects.get(name=name)
        except Permission.DoesNotExist:
            return None
    
    def create(self, **kwargs) -> Permission:
        """إنشاء صلاحية جديدة"""
        return Permission.objects.create(**kwargs)
    
    def update(self, id: int, **kwargs) -> Optional[Permission]:
        """تحديث صلاحية"""
        permission = self.get_by_id(id)
        if permission:
            for key, value in kwargs.items():
                setattr(permission, key, value)
            permission.save()
        return permission
    
    def delete(self, id: int) -> bool:
        """حذف صلاحية"""
        permission = self.get_by_id(id)
        if permission:
            permission.delete()
            return True
        return False
    
    def search(self, query: str) -> QuerySet:
        """البحث في الصلاحيات"""
        return Permission.objects.filter(
            Q(name__icontains=query) |
            Q(description__icontains=query)
        ).order_by('name')
    
    def get_active_permissions(self) -> QuerySet:
        """إرجاع الصلاحيات النشطة فقط"""
        return Permission.objects.filter(is_active=True).order_by('name')
    
    def get_by_type(self, permission_type: str) -> QuerySet:
        """إرجاع الصلاحيات حسب النوع"""
        return Permission.objects.filter(permission_type=permission_type).order_by('name')

class UserRoleRepository:
    """Repository لإدارة أدوار المستخدمين"""
    
    def assign_role(self, user_id: int, role_id: int, assigned_by_id: int = None, expires_at=None) -> Optional[UserRole]:
        """تعيين دور لمستخدم"""
        user = UserRepository().get_by_id(user_id)
        role = RoleRepository().get_by_id(role_id)
        
        if user and role:
            user_role, created = UserRole.objects.get_or_create(
                user=user,
                role=role,
                defaults={
                    'assigned_by_id': assigned_by_id,
                    'expires_at': expires_at,
                    'is_active': True
                }
            )
            if not created and not user_role.is_active:
                user_role.is_active = True
                user_role.assigned_by_id = assigned_by_id
                user_role.expires_at = expires_at
                user_role.save()
            return user_role
        return None
    
    def remove_role(self, user_id: int, role_id: int) -> bool:
        """إزالة دور من مستخدم"""
        try:
            user_role = UserRole.objects.get(
                user_id=user_id,
                role_id=role_id
            )
            user_role.is_active = False
            user_role.save()
            return True
        except UserRole.DoesNotExist:
            return False
    
    def get_user_roles(self, user_id: int) -> QuerySet:
        """إرجاع أدوار مستخدم"""
        return UserRole.objects.filter(
            user_id=user_id,
            is_active=True
        ).select_related('role')

class ActivityRepository:
    """Repository لأنشطة المستخدمين"""
    
    def log_activity(self, user_id: int, action: str, description: str, 
                    ip_address: str = None, user_agent: str = None, 
                    performed_by_id: int = None) -> UserActivity:
        """تسجيل نشاط مستخدم"""
        return UserActivity.objects.create(
            user_id=user_id,
            action=action,
            description=description,
            ip_address=ip_address,
            user_agent=user_agent,
            performed_by_id=performed_by_id
        )
    
    def get_user_activities(self, user_id: int) -> QuerySet:
        """إرجاع أنشطة مستخدم معين"""
        return UserActivity.objects.filter(user_id=user_id).order_by('-timestamp')
    
    def get_all_activities(self) -> QuerySet:
        """إرجاع جميع الأنشطة"""
        return UserActivity.objects.all().order_by('-timestamp')
    
    def get_activities_by_action(self, action: str) -> QuerySet:
        """إرجاع الأنشطة حسب النوع"""
        return UserActivity.objects.filter(action=action).order_by('-timestamp')
