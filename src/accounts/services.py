from typing import Optional, List, Dict, Any
from django.contrib.auth import authenticate
from django.db import transaction
from django.utils import timezone
from rest_framework_simplejwt.tokens import RefreshToken
from .repositories import (
    UserRepository, RoleRepository, PermissionRepository,
    UserRoleRepository, ActivityRepository
)
from .cache import PermissionCache
from .models import User, Role, Permission
import logging

logger = logging.getLogger(__name__)

class UserService:
    """خدمة إدارة المستخدمين"""
    
    def __init__(self):
        self.user_repo = UserRepository()
        self.user_role_repo = UserRoleRepository()
        self.activity_repo = ActivityRepository()
    
    def create_user(self, user_data: Dict[str, Any], created_by_id: int = None) -> User:
        """إنشاء مستخدم جديد"""
        with transaction.atomic():
            user = self.user_repo.create(**user_data)
            
            # تسجيل النشاط
            self.activity_repo.log_activity(
                user_id=user.id,
                action='profile_updated',
                description=f'تم إنشاء المستخدم {user.username}',
                performed_by_id=created_by_id
            )
            
            return user
    
    def update_user(self, user_id: int, user_data: Dict[str, Any], updated_by_id: int = None) -> Optional[User]:
        """تحديث مستخدم"""
        with transaction.atomic():
            user = self.user_repo.update(user_id, **user_data)
            
            if user:
                # تسجيل النشاط
                self.activity_repo.log_activity(
                    user_id=user.id,
                    action='profile_updated',
                    description=f'تم تحديث الملف الشخصي للمستخدم {user.username}',
                    performed_by_id=updated_by_id
                )
            
            return user
    
    def delete_user(self, user_id: int, deleted_by_id: int = None) -> bool:
        """حذف مستخدم"""
        user = self.user_repo.get_by_id(user_id)
        if user:
            with transaction.atomic():
                username = user.username
                success = self.user_repo.delete(user_id)
                
                if success and deleted_by_id:
                    # تسجيل النشاط
                    self.activity_repo.log_activity(
                        user_id=deleted_by_id,
                        action='other',
                        description=f'تم حذف المستخدم {username}',
                        performed_by_id=deleted_by_id
                    )
                
                return success
        return False
    
    def assign_role_to_user(self, user_id: int, role_id: int, assigned_by_id: int = None, expires_at=None) -> bool:
        """تعيين دور لمستخدم"""
        with transaction.atomic():
            user_role = self.user_role_repo.assign_role(user_id, role_id, assigned_by_id, expires_at)
            
            if user_role:
                # إلغاء cache المستخدم
                PermissionCache.invalidate_user_cache(user_id)

                user = self.user_repo.get_by_id(user_id)
                role = RoleRepository().get_by_id(role_id)

                # تسجيل النشاط
                self.activity_repo.log_activity(
                    user_id=user_id,
                    action='role_assigned',
                    description=f'تم تعيين الدور "{role.name}" للمستخدم {user.username}',
                    performed_by_id=assigned_by_id
                )

                logger.info(f"Role {role_id} assigned to user {user_id}, cache invalidated")
                return True
        return False
    
    def remove_role_from_user(self, user_id: int, role_id: int, removed_by_id: int = None) -> bool:
        """إزالة دور من مستخدم"""
        with transaction.atomic():
            user = self.user_repo.get_by_id(user_id)
            role = RoleRepository().get_by_id(role_id)
            
            success = self.user_role_repo.remove_role(user_id, role_id)
            
            if success:
                # إلغاء cache المستخدم
                PermissionCache.invalidate_user_cache(user_id)

                # تسجيل النشاط
                self.activity_repo.log_activity(
                    user_id=user_id,
                    action='role_removed',
                    description=f'تم إزالة الدور "{role.name}" من المستخدم {user.username}',
                    performed_by_id=removed_by_id
                )

                logger.info(f"Role {role_id} removed from user {user_id}, cache invalidated")

            return success
    
    def authenticate_user(self, username: str, password: str, ip_address: str = None, user_agent: str = None) -> Optional[Dict[str, Any]]:
        """مصادقة المستخدم وإرجاع JWT tokens"""
        user = authenticate(username=username, password=password)
        
        if user and user.is_active:
            # إنشاء JWT tokens
            refresh = RefreshToken.for_user(user)
            access_token = refresh.access_token
            
            # تسجيل نشاط تسجيل الدخول
            self.activity_repo.log_activity(
                user_id=user.id,
                action='login',
                description=f'تسجيل دخول المستخدم {user.username}',
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            return {
                'user': user,
                'access_token': str(access_token),
                'refresh_token': str(refresh),
                'token_type': 'Bearer'
            }
        
        return None
    
    def logout_user(self, user_id: int, ip_address: str = None, user_agent: str = None) -> bool:
        """تسجيل خروج المستخدم"""
        user = self.user_repo.get_by_id(user_id)
        if user:
            # تسجيل نشاط تسجيل الخروج
            self.activity_repo.log_activity(
                user_id=user.id,
                action='logout',
                description=f'تسجيل خروج المستخدم {user.username}',
                ip_address=ip_address,
                user_agent=user_agent
            )
            return True
        return False
    
    def change_password(self, user_id: int, old_password: str, new_password: str) -> bool:
        """تغيير كلمة مرور المستخدم"""
        user = self.user_repo.get_by_id(user_id)
        if user and user.check_password(old_password):
            with transaction.atomic():
                user.set_password(new_password)
                user.save()
                
                # تسجيل النشاط
                self.activity_repo.log_activity(
                    user_id=user.id,
                    action='password_changed',
                    description=f'تم تغيير كلمة المرور للمستخدم {user.username}'
                )
                
                return True
        return False
    
    def get_user_permissions(self, user_id: int) -> List[str]:
        """إرجاع قائمة بأسماء صلاحيات المستخدم"""
        permissions = self.user_repo.get_user_permissions(user_id)
        return [perm.name for perm in permissions]
    
    def user_has_permission(self, user_id: int, permission_name: str) -> bool:
        """التحقق من وجود صلاحية معينة للمستخدم"""
        user = self.user_repo.get_by_id(user_id)
        if user and user.is_superuser:
            return True
        
        permissions = self.get_user_permissions(user_id)
        return permission_name in permissions

class RoleService:
    """خدمة إدارة الأدوار"""
    
    def __init__(self):
        self.role_repo = RoleRepository()
        self.permission_repo = PermissionRepository()
        self.activity_repo = ActivityRepository()
    
    def create_role(self, role_data: Dict[str, Any], created_by_id: int = None) -> Role:
        """إنشاء دور جديد"""
        with transaction.atomic():
            role = self.role_repo.create(**role_data)
            
            # تسجيل النشاط
            if created_by_id:
                self.activity_repo.log_activity(
                    user_id=created_by_id,
                    action='other',
                    description=f'تم إنشاء الدور "{role.name}"',
                    performed_by_id=created_by_id
                )
            
            return role
    
    def update_role(self, role_id: int, role_data: Dict[str, Any], updated_by_id: int = None) -> Optional[Role]:
        """تحديث دور"""
        with transaction.atomic():
            role = self.role_repo.update(role_id, **role_data)
            
            if role and updated_by_id:
                # تسجيل النشاط
                self.activity_repo.log_activity(
                    user_id=updated_by_id,
                    action='other',
                    description=f'تم تحديث الدور "{role.name}"',
                    performed_by_id=updated_by_id
                )
            
            return role
    
    def delete_role(self, role_id: int, deleted_by_id: int = None) -> bool:
        """حذف دور"""
        role = self.role_repo.get_by_id(role_id)
        if role:
            with transaction.atomic():
                role_name = role.name
                success = self.role_repo.delete(role_id)
                
                if success and deleted_by_id:
                    # تسجيل النشاط
                    self.activity_repo.log_activity(
                        user_id=deleted_by_id,
                        action='other',
                        description=f'تم حذف الدور "{role_name}"',
                        performed_by_id=deleted_by_id
                    )
                
                return success
        return False
    
    def assign_permission_to_role(self, role_id: int, permission_id: int, assigned_by_id: int = None) -> bool:
        """تعيين صلاحية لدور"""
        with transaction.atomic():
            role_permission = self.role_repo.assign_permission(role_id, permission_id, assigned_by_id)
            
            if role_permission:
                # إلغاء cache الدور وجميع المستخدمين الذين لديهم هذا الدور
                PermissionCache.invalidate_role_cache(role_id)
                PermissionCache.invalidate_all_user_caches()  # إلغاء cache جميع المستخدمين

                role = self.role_repo.get_by_id(role_id)
                permission = self.permission_repo.get_by_id(permission_id)

                # تسجيل النشاط
                if assigned_by_id:
                    self.activity_repo.log_activity(
                        user_id=assigned_by_id,
                        action='permission_granted',
                        description=f'تم تعيين الصلاحية "{permission.name}" للدور "{role.name}"',
                        performed_by_id=assigned_by_id
                    )

                logger.info(f"Permission {permission_id} assigned to role {role_id}, cache invalidated")
                return True
        return False
    
    def remove_permission_from_role(self, role_id: int, permission_id: int, removed_by_id: int = None) -> bool:
        """إزالة صلاحية من دور"""
        with transaction.atomic():
            role = self.role_repo.get_by_id(role_id)
            permission = self.permission_repo.get_by_id(permission_id)
            
            success = self.role_repo.remove_permission(role_id, permission_id)

            if success:
                # إلغاء cache الدور وجميع المستخدمين الذين لديهم هذا الدور
                PermissionCache.invalidate_role_cache(role_id)
                PermissionCache.invalidate_all_user_caches()  # إلغاء cache جميع المستخدمين

                if removed_by_id:
                    # تسجيل النشاط
                    self.activity_repo.log_activity(
                        user_id=removed_by_id,
                        action='permission_revoked',
                        description=f'تم إزالة الصلاحية "{permission.name}" من الدور "{role.name}"',
                        performed_by_id=removed_by_id
                    )

                logger.info(f"Permission {permission_id} removed from role {role_id}, cache invalidated")

            return success

class PermissionService:
    """خدمة إدارة الصلاحيات"""
    
    def __init__(self):
        self.permission_repo = PermissionRepository()
        self.activity_repo = ActivityRepository()
    
    def create_permission(self, permission_data: Dict[str, Any], created_by_id: int = None) -> Permission:
        """إنشاء صلاحية جديدة"""
        with transaction.atomic():
            permission = self.permission_repo.create(**permission_data)
            
            # تسجيل النشاط
            if created_by_id:
                self.activity_repo.log_activity(
                    user_id=created_by_id,
                    action='other',
                    description=f'تم إنشاء الصلاحية "{permission.name}"',
                    performed_by_id=created_by_id
                )
            
            return permission
    
    def update_permission(self, permission_id: int, permission_data: Dict[str, Any], updated_by_id: int = None) -> Optional[Permission]:
        """تحديث صلاحية"""
        with transaction.atomic():
            permission = self.permission_repo.update(permission_id, **permission_data)
            
            if permission and updated_by_id:
                # تسجيل النشاط
                self.activity_repo.log_activity(
                    user_id=updated_by_id,
                    action='other',
                    description=f'تم تحديث الصلاحية "{permission.name}"',
                    performed_by_id=updated_by_id
                )
            
            return permission
    
    def delete_permission(self, permission_id: int, deleted_by_id: int = None) -> bool:
        """حذف صلاحية"""
        permission = self.permission_repo.get_by_id(permission_id)
        if permission:
            with transaction.atomic():
                permission_name = permission.name
                success = self.permission_repo.delete(permission_id)
                
                if success and deleted_by_id:
                    # تسجيل النشاط
                    self.activity_repo.log_activity(
                        user_id=deleted_by_id,
                        action='other',
                        description=f'تم حذف الصلاحية "{permission_name}"',
                        performed_by_id=deleted_by_id
                    )
                
                return success
        return False
