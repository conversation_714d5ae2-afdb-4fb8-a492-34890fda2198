from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.viewsets import ModelViewSet
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView
from django.contrib.auth import get_user_model
from django.db.models import Q
from drf_spectacular.utils import extend_schema, OpenApiParameter
from drf_spectacular.types import OpenApiTypes
from django.contrib.auth.decorators import login_required
from .models import Role, Permission, UserRole, RolePermission, UserActivity
from .serializers import (
    UserSerializer, UserCreateSerializer, UserUpdateSerializer, UserProfileSerializer,
    RoleSerializer, RoleCreateSerializer, PermissionSerializer,
    UserRoleSerializer, AssignRoleSerializer, AssignPermissionSerializer,
    UserActivitySerializer, LoginSerializer, ChangePasswordSerializer
)
from .services import UserService, RoleService, PermissionService
from .authentication import get_client_ip, get_user_agent
from .permissions import (
    IsAdminOrReadOnly, IsOwnerOrAdmin, CanManageUsers, CanManageRoles,
    CanManagePermissions, CanViewActivities, CanAssignRoles, IsProfileOwner
)

User = get_user_model()

class LoginView(TokenObtainPairView):
    """API لتسجيل الدخول"""
    serializer_class = LoginSerializer

    @extend_schema(
        summary="تسجيل الدخول",
        description="تسجيل دخول المستخدم والحصول على JWT tokens",
        responses={200: "تم تسجيل الدخول بنجاح"}
    )
    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        username = serializer.validated_data['username']
        password = serializer.validated_data['password']

        # استخدام UserService للمصادقة
        user_service = UserService()
        auth_data = user_service.authenticate_user(
            username=username,
            password=password,
            ip_address=get_client_ip(request),
            user_agent=get_user_agent(request)
        )

        if auth_data:
            return Response({
                'access_token': auth_data['access_token'],
                'refresh_token': auth_data['refresh_token'],
                'token_type': auth_data['token_type'],
                'user': UserSerializer(auth_data['user']).data
            }, status=status.HTTP_200_OK)

        return Response({
            'error': 'اسم المستخدم أو كلمة المرور غير صحيحة'
        }, status=status.HTTP_401_UNAUTHORIZED)

class LogoutView(APIView):
    """API لتسجيل الخروج"""
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="تسجيل الخروج",
        description="تسجيل خروج المستخدم",
        responses={200: "تم تسجيل الخروج بنجاح"}
    )
    def post(self, request):
        user_service = UserService()
        user_service.logout_user(
            user_id=request.user.id,
            ip_address=get_client_ip(request),
            user_agent=get_user_agent(request)
        )

        return Response({
            'message': 'تم تسجيل الخروج بنجاح'
        }, status=status.HTTP_200_OK)

class UserViewSet(ModelViewSet):
    """ViewSet لإدارة المستخدمين"""
    queryset = User.objects.all().order_by('-date_joined')
    permission_classes = [CanManageUsers]

    def get_serializer_class(self):
        if self.action == 'create':
            return UserCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return UserUpdateSerializer
        return UserSerializer

    @extend_schema(
        summary="قائمة المستخدمين",
        description="إرجاع قائمة بجميع المستخدمين مع إمكانية البحث",
        parameters=[
            OpenApiParameter(name='search', type=OpenApiTypes.STR, description='البحث في المستخدمين'),
            OpenApiParameter(name='is_active', type=OpenApiTypes.BOOL, description='فلترة حسب الحالة'),
        ]
    )
    def list(self, request):
        queryset = self.get_queryset()

        # البحث
        search = request.query_params.get('search', None)
        if search:
            queryset = queryset.filter(
                Q(username__icontains=search) |
                Q(first_name__icontains=search) |
                Q(last_name__icontains=search) |
                Q(email__icontains=search)
            )

        # فلترة حسب الحالة
        is_active = request.query_params.get('is_active', None)
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @extend_schema(
        summary="إنشاء مستخدم جديد",
        description="إنشاء مستخدم جديد في النظام"
    )
    @login_required
    def create(self, request):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        user_service = UserService()
        user = user_service.create_user(
            user_data=serializer.validated_data,
            created_by_id=request.user.id
        )

        return Response(
            UserSerializer(user).data,
            status=status.HTTP_201_CREATED
        )

    @extend_schema(
        summary="تحديث مستخدم",
        description="تحديث بيانات مستخدم موجود"
    )
    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        user = self.get_object()
        serializer = self.get_serializer(user, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)

        user_service = UserService()
        updated_user = user_service.update_user(
            user_id=user.id,
            user_data=serializer.validated_data,
            updated_by_id=request.user.id
        )

        return Response(UserSerializer(updated_user).data)

    @extend_schema(
        summary="حذف مستخدم",
        description="حذف مستخدم من النظام"
    )
    def destroy(self, request, pk=None):
        user = self.get_object()

        user_service = UserService()
        success = user_service.delete_user(
            user_id=user.id,
            deleted_by_id=request.user.id
        )

        if success:
            return Response(status=status.HTTP_204_NO_CONTENT)

        return Response(
            {'error': 'فشل في حذف المستخدم'},
            status=status.HTTP_400_BAD_REQUEST
        )

class RoleViewSet(ModelViewSet):
    """ViewSet لإدارة الأدوار"""
    queryset = Role.objects.all().order_by('name')
    permission_classes = [CanManageRoles]

    def get_serializer_class(self):
        if self.action == 'create':
            return RoleCreateSerializer
        return RoleSerializer

    @extend_schema(
        summary="قائمة الأدوار",
        description="إرجاع قائمة بجميع الأدوار مع إمكانية البحث",
        parameters=[
            OpenApiParameter(name='search', type=OpenApiTypes.STR, description='البحث في الأدوار'),
            OpenApiParameter(name='is_active', type=OpenApiTypes.BOOL, description='فلترة حسب الحالة'),
        ]
    )
    def list(self, request):
        queryset = self.get_queryset()

        # البحث
        search = request.query_params.get('search', None)
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(description__icontains=search)
            )

        # فلترة حسب الحالة
        is_active = request.query_params.get('is_active', None)
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @extend_schema(
        summary="إنشاء دور جديد",
        description="إنشاء دور جديد في النظام"
    )
    def create(self, request):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        role_service = RoleService()
        role = role_service.create_role(
            role_data=serializer.validated_data,
            created_by_id=request.user.id
        )

        return Response(
            RoleSerializer(role).data,
            status=status.HTTP_201_CREATED
        )

    @extend_schema(
        summary="تحديث دور",
        description="تحديث بيانات دور موجود"
    )
    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        role = self.get_object()
        serializer = self.get_serializer(role, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)

        role_service = RoleService()
        updated_role = role_service.update_role(
            role_id=role.id,
            role_data=serializer.validated_data,
            updated_by_id=request.user.id
        )

        return Response(RoleSerializer(updated_role).data)

    @extend_schema(
        summary="حذف دور",
        description="حذف دور من النظام"
    )
    def destroy(self, request, pk=None):
        role = self.get_object()

        role_service = RoleService()
        success = role_service.delete_role(
            role_id=role.id,
            deleted_by_id=request.user.id
        )

        if success:
            return Response(status=status.HTTP_204_NO_CONTENT)

        return Response(
            {'error': 'فشل في حذف الدور'},
            status=status.HTTP_400_BAD_REQUEST
        )

class PermissionViewSet(ModelViewSet):
    """ViewSet لإدارة الصلاحيات"""
    queryset = Permission.objects.all().order_by('name')
    permission_classes = [CanManagePermissions]
    serializer_class = PermissionSerializer

    @extend_schema(
        summary="قائمة الصلاحيات",
        description="إرجاع قائمة بجميع الصلاحيات مع إمكانية البحث",
        parameters=[
            OpenApiParameter(name='search', type=OpenApiTypes.STR, description='البحث في الصلاحيات'),
            OpenApiParameter(name='permission_type', type=OpenApiTypes.STR, description='فلترة حسب النوع'),
        ]
    )
    def list(self, request):
        queryset = self.get_queryset()

        # البحث
        search = request.query_params.get('search', None)
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(description__icontains=search)
            )

        # فلترة حسب النوع
        permission_type = request.query_params.get('permission_type', None)
        if permission_type:
            queryset = queryset.filter(permission_type=permission_type)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @extend_schema(
        summary="إنشاء صلاحية جديدة",
        description="إنشاء صلاحية جديدة في النظام"
    )
    def create(self, request):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        permission_service = PermissionService()
        permission = permission_service.create_permission(
            permission_data=serializer.validated_data,
            created_by_id=request.user.id
        )

        return Response(
            PermissionSerializer(permission).data,
            status=status.HTTP_201_CREATED
        )

@api_view(['POST'])
@permission_classes([CanAssignRoles])
@extend_schema(
    summary="تعيين دور لمستخدم",
    description="تعيين دور معين لمستخدم",
    request=AssignRoleSerializer
)
def assign_role_to_user(request):
    """تعيين دور لمستخدم"""
    serializer = AssignRoleSerializer(data=request.data)
    serializer.is_valid(raise_exception=True)

    user_service = UserService()
    success = user_service.assign_role_to_user(
        user_id=serializer.validated_data['user_id'],
        role_id=serializer.validated_data['role_id'],
        assigned_by_id=request.user.id,
        expires_at=serializer.validated_data.get('expires_at')
    )

    if success:
        return Response({'message': 'تم تعيين الدور بنجاح'}, status=status.HTTP_200_OK)

    return Response({'error': 'فشل في تعيين الدور'}, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST'])
@permission_classes([CanAssignRoles])
@extend_schema(
    summary="إزالة دور من مستخدم",
    description="إزالة دور معين من مستخدم"
)
def remove_role_from_user(request):
    """إزالة دور من مستخدم"""
    user_id = request.data.get('user_id')
    role_id = request.data.get('role_id')

    if not user_id or not role_id:
        return Response(
            {'error': 'يجب تحديد معرف المستخدم ومعرف الدور'},
            status=status.HTTP_400_BAD_REQUEST
        )

    user_service = UserService()
    success = user_service.remove_role_from_user(
        user_id=user_id,
        role_id=role_id,
        removed_by_id=request.user.id
    )

    if success:
        return Response({'message': 'تم إزالة الدور بنجاح'}, status=status.HTTP_200_OK)

    return Response({'error': 'فشل في إزالة الدور'}, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST'])
@permission_classes([CanManageRoles])
@extend_schema(
    summary="تعيين صلاحية لدور",
    description="تعيين صلاحية معينة لدور",
    request=AssignPermissionSerializer
)
def assign_permission_to_role(request):
    """تعيين صلاحية لدور"""
    serializer = AssignPermissionSerializer(data=request.data)
    serializer.is_valid(raise_exception=True)

    role_service = RoleService()
    success = role_service.assign_permission_to_role(
        role_id=serializer.validated_data['role_id'],
        permission_id=serializer.validated_data['permission_id'],
        assigned_by_id=request.user.id
    )

    if success:
        return Response({'message': 'تم تعيين الصلاحية بنجاح'}, status=status.HTTP_200_OK)

    return Response({'error': 'فشل في تعيين الصلاحية'}, status=status.HTTP_400_BAD_REQUEST)

@api_view(['POST'])
@permission_classes([CanManageRoles])
@extend_schema(
    summary="إزالة صلاحية من دور",
    description="إزالة صلاحية معينة من دور"
)
def remove_permission_from_role(request):
    """إزالة صلاحية من دور"""
    role_id = request.data.get('role_id')
    permission_id = request.data.get('permission_id')

    if not role_id or not permission_id:
        return Response(
            {'error': 'يجب تحديد معرف الدور ومعرف الصلاحية'},
            status=status.HTTP_400_BAD_REQUEST
        )

    role_service = RoleService()
    success = role_service.remove_permission_from_role(
        role_id=role_id,
        permission_id=permission_id,
        removed_by_id=request.user.id
    )

    if success:
        return Response({'message': 'تم إزالة الصلاحية بنجاح'}, status=status.HTTP_200_OK)

    return Response({'error': 'فشل في إزالة الصلاحية'}, status=status.HTTP_400_BAD_REQUEST)

class UserProfileView(APIView):
    """API لإدارة الملف الشخصي للمستخدم"""
    permission_classes = [IsProfileOwner]

    @extend_schema(
        summary="عرض الملف الشخصي",
        description="عرض الملف الشخصي للمستخدم الحالي"
    )
    def get(self, request):
        serializer = UserProfileSerializer(request.user)
        return Response(serializer.data)

    @extend_schema(
        summary="تحديث الملف الشخصي",
        description="تحديث الملف الشخصي للمستخدم الحالي"
    )
    def put(self, request):
        serializer = UserProfileSerializer(request.user, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)

        user_service = UserService()
        updated_user = user_service.update_user(
            user_id=request.user.id,
            user_data=serializer.validated_data,
            updated_by_id=request.user.id
        )

        return Response(UserProfileSerializer(updated_user).data)

class ChangePasswordView(APIView):
    """API لتغيير كلمة المرور"""
    permission_classes = [permissions.IsAuthenticated]

    @extend_schema(
        summary="تغيير كلمة المرور",
        description="تغيير كلمة مرور المستخدم الحالي",
        request=ChangePasswordSerializer
    )
    def post(self, request):
        serializer = ChangePasswordSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        user_service = UserService()
        success = user_service.change_password(
            user_id=request.user.id,
            old_password=serializer.validated_data['old_password'],
            new_password=serializer.validated_data['new_password']
        )

        if success:
            return Response({'message': 'تم تغيير كلمة المرور بنجاح'}, status=status.HTTP_200_OK)

        return Response(
            {'error': 'كلمة المرور الحالية غير صحيحة'},
            status=status.HTTP_400_BAD_REQUEST
        )

class UserActivitiesView(APIView):
    """API لعرض أنشطة المستخدمين"""
    permission_classes = [CanViewActivities]

    @extend_schema(
        summary="عرض الأنشطة",
        description="عرض أنشطة المستخدمين",
        parameters=[
            OpenApiParameter(name='user_id', type=OpenApiTypes.INT, description='معرف المستخدم'),
            OpenApiParameter(name='action', type=OpenApiTypes.STR, description='نوع النشاط'),
        ]
    )
    def get(self, request):
        queryset = UserActivity.objects.all().order_by('-timestamp')

        # فلترة حسب المستخدم
        user_id = request.query_params.get('user_id', None)
        if user_id:
            queryset = queryset.filter(user_id=user_id)

        # فلترة حسب نوع النشاط
        action = request.query_params.get('action', None)
        if action:
            queryset = queryset.filter(action=action)

        # إذا لم يكن مدير عام، عرض أنشطته فقط
        if not request.user.is_superuser:
            queryset = queryset.filter(user=request.user)

        # Pagination
        from rest_framework.pagination import PageNumberPagination
        paginator = PageNumberPagination()
        paginator.page_size = 20
        page = paginator.paginate_queryset(queryset, request)

        if page is not None:
            serializer = UserActivitySerializer(page, many=True)
            return paginator.get_paginated_response(serializer.data)

        serializer = UserActivitySerializer(queryset, many=True)
        return Response(serializer.data)
