from rest_framework_simplejwt.authentication import J<PERSON><PERSON>uthentication
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from rest_framework.authentication import BaseAuthentication
from rest_framework.exceptions import AuthenticationFailed
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _

User = get_user_model()

class CustomJWTAuthentication(JWTAuthentication):
    """JWT Authentication مخصص"""
    
    def authenticate(self, request):
        """مصادقة المستخدم باستخدام JWT token"""
        header = self.get_header(request)
        if header is None:
            return None

        raw_token = self.get_raw_token(header)
        if raw_token is None:
            return None

        validated_token = self.get_validated_token(raw_token)
        user = self.get_user(validated_token)
        
        # التحقق من أن المستخدم نشط
        if not user.is_active:
            raise AuthenticationFailed(_('User account is disabled.'))
        
        return (user, validated_token)
    
    def get_user(self, validated_token):
        """إرجاع المستخدم من الـ token"""
        try:
            user_id = validated_token['user_id']
        except KeyError:
            raise InvalidToken(_('Token contained no recognizable user identification'))

        try:
            user = User.objects.get(**{self.user_model.USERNAME_FIELD: user_id})
        except User.DoesNotExist:
            raise AuthenticationFailed(_('User not found'), code='user_not_found')

        if not user.is_active:
            raise AuthenticationFailed(_('User is inactive'), code='user_inactive')

        return user

def get_client_ip(request):
    """الحصول على عنوان IP الحقيقي للمستخدم"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip

def get_user_agent(request):
    """الحصول على معلومات المتصفح"""
    return request.META.get('HTTP_USER_AGENT', '')
