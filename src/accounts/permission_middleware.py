from django.http import JsonResponse
from django.urls import resolve
from django.conf import settings
from .cache import PermissionCache
from .services import UserService
import logging
import time

logger = logging.getLogger(__name__)

class PermissionCheckMiddleware:
    """
    Middleware للتحقق من الصلاحيات تلقائياً لكل route
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        
        # تعريف الصلاحيات المطلوبة لكل route
        self.route_permissions = {
            # User management routes
            'accounts:user-list': {
                'GET': ['قراءة المستخدمين'],
                'POST': ['إضافة مستخدمين'],
            },
            'accounts:user-detail': {
                'GET': ['قراءة المستخدمين'],
                'PUT': ['تعديل المستخدمين'],
                'PATCH': ['تعديل المستخدمين'],
                'DELETE': ['حذف المستخدمين'],
            },
            
            # Role management routes
            'accounts:role-list': {
                'GET': ['قراءة الأدوار'],
                'POST': ['إدارة الأدوار'],
            },
            'accounts:role-detail': {
                'GET': ['قراءة الأدوار'],
                'PUT': ['إدارة الأدوار'],
                'PATCH': ['إدارة الأدوار'],
                'DELETE': ['إدارة الأدوار'],
            },
            
            # Permission management routes
            'accounts:permission-list': {
                'GET': ['قراءة الصلاحيات'],
                'POST': ['إدارة الصلاحيات'],
            },
            'accounts:permission-detail': {
                'GET': ['قراءة الصلاحيات'],
                'PUT': ['إدارة الصلاحيات'],
                'PATCH': ['إدارة الصلاحيات'],
                'DELETE': ['إدارة الصلاحيات'],
            },
            
            # Role assignment routes
            'accounts:assign-role': {
                'POST': ['تعيين الأدوار', 'إدارة الأدوار'],
            },
            'accounts:remove-role': {
                'POST': ['تعيين الأدوار', 'إدارة الأدوار'],
            },
            'accounts:assign-permission': {
                'POST': ['إدارة الصلاحيات'],
            },
            'accounts:remove-permission': {
                'POST': ['إدارة الصلاحيات'],
            },
            
            # Activity routes
            'accounts:activities': {
                'GET': ['عرض الأنشطة'],
            },
        }
        
        # Routes التي لا تحتاج فحص صلاحيات
        self.exempt_routes = [
            'accounts:login',
            'accounts:logout', 
            'accounts:refresh',
            'accounts:profile',
            'accounts:change-password',
        ]
    
    def __call__(self, request):
        start_time = time.time()
        
        # تخطي فحص الصلاحيات للـ routes المستثناة
        if self._is_exempt_route(request):
            response = self.get_response(request)
            return response
        
        # تخطي فحص الصلاحيات للمديرين العامين
        if request.user.is_authenticated and request.user.is_superuser:
            response = self.get_response(request)
            return response
        
        # فحص الصلاحيات
        permission_check_result = self._check_permissions(request)
        
        if not permission_check_result['allowed']:
            logger.warning(
                f"Permission denied for user {request.user.id if request.user.is_authenticated else 'anonymous'} "
                f"on route {permission_check_result['route_name']} with method {request.method}. "
                f"Required permissions: {permission_check_result['required_permissions']}"
            )
            
            return JsonResponse({
                'error': 'ليس لديك الصلاحية للوصول لهذا المورد',
                'required_permissions': permission_check_result['required_permissions'],
                'route': permission_check_result['route_name']
            }, status=403)
        
        response = self.get_response(request)
        
        # تسجيل وقت المعالجة
        processing_time = time.time() - start_time
        logger.debug(f"Permission check completed in {processing_time:.3f}s for {request.path}")
        
        return response
    
    def _is_exempt_route(self, request):
        """التحقق من أن الـ route مستثنى من فحص الصلاحيات"""
        try:
            resolved = resolve(request.path)
            route_name = f"{resolved.namespace}:{resolved.url_name}" if resolved.namespace else resolved.url_name
            return route_name in self.exempt_routes
        except:
            return False
    
    def _check_permissions(self, request):
        """فحص صلاحيات المستخدم للـ route المطلوب"""
        result = {
            'allowed': True,
            'route_name': None,
            'required_permissions': [],
            'user_permissions': []
        }
        
        try:
            # الحصول على معلومات الـ route
            resolved = resolve(request.path)
            route_name = f"{resolved.namespace}:{resolved.url_name}" if resolved.namespace else resolved.url_name
            result['route_name'] = route_name
            
            # التحقق من أن المستخدم مسجل دخول
            if not request.user.is_authenticated:
                result['allowed'] = False
                result['required_permissions'] = ['تسجيل الدخول مطلوب']
                return result
            
            # الحصول على الصلاحيات المطلوبة للـ route
            route_permissions = self.route_permissions.get(route_name, {})
            method_permissions = route_permissions.get(request.method, [])
            
            if not method_permissions:
                # إذا لم تكن هناك صلاحيات محددة، السماح بالوصول
                return result
            
            result['required_permissions'] = method_permissions
            
            # فحص صلاحيات المستخدم باستخدام الـ cache
            user_has_permission = self._user_has_any_permission(
                request.user.id, 
                method_permissions
            )
            
            result['allowed'] = user_has_permission
            
            # الحصول على صلاحيات المستخدم للتسجيل
            if not user_has_permission:
                cached_permissions = PermissionCache.get_user_permissions(request.user.id)
                if cached_permissions:
                    result['user_permissions'] = list(cached_permissions)
            
            return result
            
        except Exception as e:
            logger.error(f"Error checking permissions: {e}")
            # في حالة الخطأ، منع الوصول للأمان
            result['allowed'] = False
            result['required_permissions'] = ['خطأ في فحص الصلاحيات']
            return result
    
    def _user_has_any_permission(self, user_id: int, required_permissions: list) -> bool:
        """التحقق من أن المستخدم لديه أي من الصلاحيات المطلوبة"""
        try:
            # محاولة الحصول على الصلاحيات من الـ cache
            cached_permissions = PermissionCache.get_user_permissions(user_id)
            
            if cached_permissions is not None:
                # إذا وُجدت في الـ cache، تحقق من الصلاحيات
                has_permission = any(perm in cached_permissions for perm in required_permissions)
                logger.debug(f"Cache hit: User {user_id} has any of {required_permissions}: {has_permission}")
                return has_permission
            
            # إذا لم توجد في الـ cache، احصل عليها من قاعدة البيانات
            user_service = UserService()
            all_permissions = set(user_service.get_user_permissions(user_id))
            
            # خزن الصلاحيات في الـ cache
            PermissionCache.set_user_permissions(user_id, all_permissions)
            
            has_permission = any(perm in all_permissions for perm in required_permissions)
            logger.debug(f"Cache miss: User {user_id} has any of {required_permissions}: {has_permission}")
            return has_permission
            
        except Exception as e:
            logger.error(f"Error checking user permissions: {e}")
            return False

class CacheWarmupMiddleware:
    """
    Middleware لتحميل الـ cache مسبقاً للمستخدمين النشطين
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # تحميل cache المستخدم إذا لم يكن موجوداً
        if request.user.is_authenticated:
            self._warm_up_user_cache(request.user.id)
        
        response = self.get_response(request)
        return response
    
    def _warm_up_user_cache(self, user_id: int):
        """تحميل cache المستخدم مسبقاً"""
        try:
            # التحقق من وجود الصلاحيات في الـ cache
            cached_permissions = PermissionCache.get_user_permissions(user_id)
            
            if cached_permissions is None:
                # إذا لم توجد، قم بتحميلها
                PermissionCache.warm_up_user_cache(user_id)
                logger.debug(f"Warmed up cache for user {user_id}")
                
        except Exception as e:
            logger.error(f"Error warming up cache for user {user_id}: {e}")

class PermissionLoggingMiddleware:
    """
    Middleware لتسجيل محاولات الوصول والصلاحيات
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # تسجيل محاولة الوصول
        if request.user.is_authenticated:
            logger.info(
                f"User {request.user.username} (ID: {request.user.id}) "
                f"accessing {request.method} {request.path}"
            )
        
        response = self.get_response(request)
        
        # تسجيل نتيجة الوصول
        if response.status_code == 403:
            logger.warning(
                f"Access denied for user {request.user.id if request.user.is_authenticated else 'anonymous'} "
                f"to {request.method} {request.path}"
            )
        
        return response
