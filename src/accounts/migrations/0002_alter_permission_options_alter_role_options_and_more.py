# Generated by Django 5.2.6 on 2025-09-08 23:39

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0001_initial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='permission',
            options={'verbose_name': 'صلاحية', 'verbose_name_plural': 'الصلاحيات'},
        ),
        migrations.AlterModelOptions(
            name='role',
            options={'verbose_name': 'دور', 'verbose_name_plural': 'الأدوار'},
        ),
        migrations.AlterModelOptions(
            name='rolepermission',
            options={'verbose_name': 'صلاحية الدور', 'verbose_name_plural': 'صلاحيات الأدوار'},
        ),
        migrations.AlterModelOptions(
            name='user',
            options={'verbose_name': 'مستخدم', 'verbose_name_plural': 'المستخدمون'},
        ),
        migrations.AlterModelOptions(
            name='userrole',
            options={'verbose_name': 'دور المستخدم', 'verbose_name_plural': 'أدوار المستخدمين'},
        ),
        migrations.RemoveField(
            model_name='role',
            name='permissions',
        ),
        migrations.RemoveField(
            model_name='userrole',
            name='notes',
        ),
        migrations.AddField(
            model_name='permission',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث'),
        ),
        migrations.AlterField(
            model_name='permission',
            name='description',
            field=models.TextField(blank=True, null=True, verbose_name='الوصف'),
        ),
        migrations.AlterField(
            model_name='permission',
            name='permission_type',
            field=models.CharField(choices=[('read', 'قراءة'), ('write', 'كتابة'), ('delete', 'حذف'), ('manage', 'إدارة')], default='read', max_length=20, verbose_name='نوع الصلاحية'),
        ),
        migrations.AlterField(
            model_name='role',
            name='description',
            field=models.TextField(blank=True, null=True, verbose_name='الوصف'),
        ),
        migrations.AlterField(
            model_name='rolepermission',
            name='assigned_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_permissions', to=settings.AUTH_USER_MODEL, verbose_name='تم التعيين بواسطة'),
        ),
        migrations.AlterField(
            model_name='rolepermission',
            name='permission',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='permission_roles', to='accounts.permission', verbose_name='الصلاحية'),
        ),
        migrations.AlterField(
            model_name='rolepermission',
            name='role',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='role_permissions', to='accounts.role', verbose_name='الدور'),
        ),
        migrations.AlterField(
            model_name='user',
            name='phone_number',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهاتف'),
        ),
        migrations.AlterField(
            model_name='user',
            name='profile_picture',
            field=models.ImageField(blank=True, null=True, upload_to='profiles/', verbose_name='صورة الملف الشخصي'),
        ),
        migrations.AlterField(
            model_name='useractivity',
            name='action',
            field=models.CharField(choices=[('login', 'تسجيل دخول'), ('logout', 'تسجيل خروج'), ('role_assigned', 'تعيين دور'), ('role_removed', 'إزالة دور'), ('permission_granted', 'منح صلاحية'), ('permission_revoked', 'إلغاء صلاحية'), ('profile_updated', 'تحديث الملف الشخصي'), ('password_changed', 'تغيير كلمة المرور'), ('other', 'أخرى')], max_length=50, verbose_name='النشاط'),
        ),
        migrations.AlterField(
            model_name='useractivity',
            name='description',
            field=models.TextField(verbose_name='الوصف'),
        ),
        migrations.AlterField(
            model_name='useractivity',
            name='user_agent',
            field=models.TextField(blank=True, null=True, verbose_name='معلومات المتصفح'),
        ),
        migrations.AlterField(
            model_name='userrole',
            name='assigned_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_roles', to=settings.AUTH_USER_MODEL, verbose_name='تم التعيين بواسطة'),
        ),
        migrations.AlterModelTable(
            name='permission',
            table='accounts_permission',
        ),
        migrations.AlterModelTable(
            name='role',
            table='accounts_role',
        ),
        migrations.AlterModelTable(
            name='rolepermission',
            table='accounts_role_permission',
        ),
        migrations.AlterModelTable(
            name='user',
            table='accounts_user',
        ),
        migrations.AlterModelTable(
            name='useractivity',
            table='accounts_user_activity',
        ),
        migrations.AlterModelTable(
            name='userrole',
            table='accounts_user_role',
        ),
    ]
