# Generated by Django 5.2.6 on 2025-09-08 23:01

import django.contrib.auth.models
import django.contrib.auth.validators
import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='Permission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='اسم الصلاحية')),
                ('description', models.TextField(blank=True, verbose_name='وصف الصلاحية')),
                ('permission_type', models.CharField(choices=[('read', 'قراءة'), ('write', 'كتابة'), ('delete', 'حذف'), ('manage', 'إدارة')], default='read', max_length=10, verbose_name='نوع الصلاحية')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
            ],
            options={
                'verbose_name': 'صلاحية',
                'verbose_name_plural': 'الصلاحيات',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Role',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='اسم الدور')),
                ('description', models.TextField(blank=True, verbose_name='وصف الدور')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'دور',
                'verbose_name_plural': 'الأدوار',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='email address')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('phone_number', models.CharField(blank=True, max_length=17, validators=[django.core.validators.RegexValidator(message="رقم الهاتف يجب أن يكون بالصيغة: '+999999999'. حتى 15 رقم مسموح.", regex='^\\+?1?\\d{9,15}$')], verbose_name='رقم الهاتف')),
                ('birth_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الميلاد')),
                ('profile_picture', models.ImageField(blank=True, null=True, upload_to='profile_pics/', verbose_name='الصورة الشخصية')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
            ],
            options={
                'verbose_name': 'مستخدم',
                'verbose_name_plural': 'المستخدمون',
                'ordering': ['-created_at'],
            },
            managers=[
                ('objects', django.contrib.auth.models.UserManager()),
            ],
        ),
        migrations.CreateModel(
            name='RolePermission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('assigned_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التعيين')),
                ('assigned_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_role_permissions', to=settings.AUTH_USER_MODEL, verbose_name='تم التعيين بواسطة')),
                ('permission', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounts.permission', verbose_name='الصلاحية')),
                ('role', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounts.role', verbose_name='الدور')),
            ],
            options={
                'verbose_name': 'صلاحية الدور',
                'verbose_name_plural': 'صلاحيات الأدوار',
                'ordering': ['role__name', 'permission__name'],
                'unique_together': {('role', 'permission')},
            },
        ),
        migrations.AddField(
            model_name='role',
            name='permissions',
            field=models.ManyToManyField(through='accounts.RolePermission', to='accounts.permission', verbose_name='الصلاحيات'),
        ),
        migrations.CreateModel(
            name='UserActivity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('login', 'تسجيل دخول'), ('logout', 'تسجيل خروج'), ('role_assigned', 'تعيين دور'), ('role_removed', 'إزالة دور'), ('permission_granted', 'منح صلاحية'), ('permission_revoked', 'إلغاء صلاحية'), ('profile_updated', 'تحديث الملف الشخصي'), ('password_changed', 'تغيير كلمة المرور')], max_length=20, verbose_name='النشاط')),
                ('description', models.TextField(blank=True, verbose_name='الوصف')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='عنوان IP')),
                ('user_agent', models.TextField(blank=True, verbose_name='معلومات المتصفح')),
                ('timestamp', models.DateTimeField(auto_now_add=True, verbose_name='الوقت')),
                ('performed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='performed_activities', to=settings.AUTH_USER_MODEL, verbose_name='تم بواسطة')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='activities', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'نشاط المستخدم',
                'verbose_name_plural': 'أنشطة المستخدمين',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='UserRole',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('assigned_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التعيين')),
                ('expires_at', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('assigned_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_user_roles', to=settings.AUTH_USER_MODEL, verbose_name='تم التعيين بواسطة')),
                ('role', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='role_users', to='accounts.role', verbose_name='الدور')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_roles', to=settings.AUTH_USER_MODEL, verbose_name='المستخدم')),
            ],
            options={
                'verbose_name': 'دور المستخدم',
                'verbose_name_plural': 'أدوار المستخدمين',
                'ordering': ['user__username', 'role__name'],
                'unique_together': {('user', 'role')},
            },
        ),
    ]
