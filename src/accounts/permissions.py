from rest_framework import permissions
from .services import UserService
from .cache import PermissionCache
import logging

logger = logging.getLogger(__name__)

def check_user_permission_cached(user_id: int, permission_name: str) -> bool:
    """
    التحقق من صلاحية المستخدم مع استخدام الـ cache
    """
    try:
        # محاولة الحصول على الصلاحيات من الـ cache
        cached_permissions = PermissionCache.get_user_permissions(user_id)

        if cached_permissions is not None:
            # إذا وُجدت في الـ cache، تحقق من الصلاحية
            has_permission = permission_name in cached_permissions
            logger.debug(f"Cache hit: User {user_id} permission '{permission_name}': {has_permission}")
            return has_permission

        # إذا لم توجد في الـ cache، احصل عليها من قاعدة البيانات
        user_service = UserService()
        has_permission = user_service.user_has_permission(user_id, permission_name)

        # احصل على جميع صلاحيات المستخدم وخزنها في الـ cache
        all_permissions = set(user_service.get_user_permissions(user_id))
        PermissionCache.set_user_permissions(user_id, all_permissions)

        logger.debug(f"Cache miss: User {user_id} permission '{permission_name}': {has_permission}")
        return has_permission

    except Exception as e:
        logger.error(f"Error checking permission for user {user_id}: {e}")
        # في حالة الخطأ، ارجع إلى الطريقة التقليدية
        user_service = UserService()
        return user_service.user_has_permission(user_id, permission_name)

def check_multiple_permissions_cached(user_id: int, permission_names: list) -> bool:
    """
    التحقق من عدة صلاحيات للمستخدم مع استخدام الـ cache
    """
    try:
        # محاولة الحصول على الصلاحيات من الـ cache
        cached_permissions = PermissionCache.get_user_permissions(user_id)

        if cached_permissions is not None:
            # إذا وُجدت في الـ cache، تحقق من الصلاحيات
            has_any = any(perm in cached_permissions for perm in permission_names)
            logger.debug(f"Cache hit: User {user_id} has any of {permission_names}: {has_any}")
            return has_any

        # إذا لم توجد في الـ cache، احصل عليها من قاعدة البيانات
        user_service = UserService()
        all_permissions = set(user_service.get_user_permissions(user_id))

        # خزن الصلاحيات في الـ cache
        PermissionCache.set_user_permissions(user_id, all_permissions)

        has_any = any(perm in all_permissions for perm in permission_names)
        logger.debug(f"Cache miss: User {user_id} has any of {permission_names}: {has_any}")
        return has_any

    except Exception as e:
        logger.error(f"Error checking multiple permissions for user {user_id}: {e}")
        # في حالة الخطأ، ارجع إلى الطريقة التقليدية
        user_service = UserService()
        return any(user_service.user_has_permission(user_id, perm) for perm in permission_names)

class IsAdminOrReadOnly(permissions.BasePermission):
    """
    صلاحية للمديرين فقط للكتابة، والقراءة للجميع
    """
    
    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return request.user.is_authenticated
        return request.user.is_authenticated and request.user.is_staff

class IsOwnerOrAdmin(permissions.BasePermission):
    """
    صلاحية للمالك أو المدير فقط
    """
    
    def has_object_permission(self, request, view, obj):
        # المدير العام يمكنه الوصول لكل شيء
        if request.user.is_superuser:
            return True
        
        # المالك يمكنه الوصول لبياناته
        if hasattr(obj, 'user'):
            return obj.user == request.user
        
        # إذا كان الكائن هو المستخدم نفسه
        if obj == request.user:
            return True
        
        return False

class CanManageUsers(permissions.BasePermission):
    """
    صلاحية إدارة المستخدمين مع استخدام الـ cache
    """

    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False

        # المدير العام يمكنه إدارة المستخدمين
        if request.user.is_superuser:
            return True

        # التحقق من الصلاحيات المخصصة باستخدام الـ cache
        user_id = request.user.id

        # للقراءة
        if request.method in permissions.SAFE_METHODS:
            return check_user_permission_cached(user_id, 'قراءة المستخدمين')

        # للكتابة والتعديل
        if request.method in ['POST', 'PUT', 'PATCH']:
            return check_multiple_permissions_cached(
                user_id,
                ['إضافة مستخدمين', 'تعديل المستخدمين']
            )

        # للحذف
        if request.method == 'DELETE':
            return check_user_permission_cached(user_id, 'حذف المستخدمين')

        return False

class CanManageRoles(permissions.BasePermission):
    """
    صلاحية إدارة الأدوار مع استخدام الـ cache
    """

    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False

        # المدير العام يمكنه إدارة الأدوار
        if request.user.is_superuser:
            return True

        # التحقق من الصلاحيات المخصصة باستخدام الـ cache
        return check_user_permission_cached(request.user.id, 'إدارة الأدوار')

class CanManagePermissions(permissions.BasePermission):
    """
    صلاحية إدارة الصلاحيات مع استخدام الـ cache
    """

    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False

        # المدير العام فقط يمكنه إدارة الصلاحيات
        if request.user.is_superuser:
            return True

        # التحقق من الصلاحيات المخصصة باستخدام الـ cache
        return check_user_permission_cached(request.user.id, 'إدارة الصلاحيات')

class CanViewActivities(permissions.BasePermission):
    """
    صلاحية عرض الأنشطة مع استخدام الـ cache
    """

    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False

        # المدير العام يمكنه عرض جميع الأنشطة
        if request.user.is_superuser:
            return True

        # التحقق من الصلاحيات المخصصة باستخدام الـ cache
        return check_user_permission_cached(request.user.id, 'عرض الأنشطة')
    
    def has_object_permission(self, request, view, obj):
        # المدير العام يمكنه عرض جميع الأنشطة
        if request.user.is_superuser:
            return True
        
        # المستخدم يمكنه عرض أنشطته فقط
        return obj.user == request.user

class CanAssignRoles(permissions.BasePermission):
    """
    صلاحية تعيين الأدوار مع استخدام الـ cache
    """

    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False

        # المدير العام يمكنه تعيين الأدوار
        if request.user.is_superuser:
            return True

        # التحقق من الصلاحيات المخصصة باستخدام الـ cache
        return check_multiple_permissions_cached(
            request.user.id,
            ['تعيين الأدوار', 'إدارة الأدوار']
        )

class IsProfileOwner(permissions.BasePermission):
    """
    صلاحية للوصول للملف الشخصي
    """
    
    def has_permission(self, request, view):
        return request.user.is_authenticated
    
    def has_object_permission(self, request, view, obj):
        # المدير العام يمكنه الوصول لجميع الملفات
        if request.user.is_superuser:
            return True
        
        # المستخدم يمكنه الوصول لملفه الشخصي فقط
        return obj == request.user
