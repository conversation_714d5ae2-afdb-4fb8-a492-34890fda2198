from django.utils.deprecation import MiddlewareMixin
from django.http import JsonResponse
from django.contrib.auth import get_user_model
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from .repositories import ActivityRepository
from .authentication import get_client_ip, get_user_agent
import json

User = get_user_model()

class APIActivityLoggingMiddleware(MiddlewareMixin):
    """Middleware لتسجيل أنشطة API"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.activity_repo = ActivityRepository()
        super().__init__(get_response)
    
    def __call__(self, request):
        # تسجيل النشاط قبل معالجة الطلب
        self.log_request(request)
        
        response = self.get_response(request)
        
        # تسجيل النشاط بعد معالجة الطلب
        self.log_response(request, response)
        
        return response
    
    def log_request(self, request):
        """تسجيل طلب API"""
        # تسجيل الطلبات للـ API فقط
        if not request.path.startswith('/api/'):
            return
        
        # تجاهل طلبات المصادقة والتوثيق
        if any(path in request.path for path in ['/auth/', '/schema/', '/docs/', '/redoc/']):
            return
        
        # الحصول على المستخدم من JWT token
        user = self.get_user_from_token(request)
        if not user:
            return
        
        # تحديد نوع النشاط حسب HTTP method
        action_map = {
            'GET': 'api_read',
            'POST': 'api_create',
            'PUT': 'api_update',
            'PATCH': 'api_update',
            'DELETE': 'api_delete'
        }
        
        action = action_map.get(request.method, 'api_other')
        
        # وصف النشاط
        description = f"{request.method} {request.path}"
        if request.method in ['POST', 'PUT', 'PATCH'] and hasattr(request, 'body'):
            try:
                # إضافة معلومات إضافية للطلبات التي تحتوي على بيانات
                if request.content_type == 'application/json':
                    body = json.loads(request.body.decode('utf-8'))
                    if 'password' in body:
                        body['password'] = '***'
                    description += f" - Data: {str(body)[:100]}"
            except:
                pass
        
        # تسجيل النشاط
        self.activity_repo.log_activity(
            user_id=user.id,
            action=action,
            description=description,
            ip_address=get_client_ip(request),
            user_agent=get_user_agent(request)
        )
    
    def log_response(self, request, response):
        """تسجيل استجابة API"""
        # تسجيل الاستجابات للـ API فقط
        if not request.path.startswith('/api/'):
            return
        
        # تجاهل طلبات المصادقة والتوثيق
        if any(path in request.path for path in ['/auth/', '/schema/', '/docs/', '/redoc/']):
            return
        
        # الحصول على المستخدم من JWT token
        user = self.get_user_from_token(request)
        if not user:
            return
        
        # تسجيل الأخطاء فقط
        if response.status_code >= 400:
            self.activity_repo.log_activity(
                user_id=user.id,
                action='api_error',
                description=f"API Error {response.status_code} - {request.method} {request.path}",
                ip_address=get_client_ip(request),
                user_agent=get_user_agent(request)
            )
    
    def get_user_from_token(self, request):
        """استخراج المستخدم من JWT token"""
        try:
            jwt_auth = JWTAuthentication()
            header = jwt_auth.get_header(request)
            if header is None:
                return None
            
            raw_token = jwt_auth.get_raw_token(header)
            if raw_token is None:
                return None
            
            validated_token = jwt_auth.get_validated_token(raw_token)
            user = jwt_auth.get_user(validated_token)
            
            return user if user.is_authenticated else None
        except (InvalidToken, TokenError, Exception):
            return None

class CORSMiddleware(MiddlewareMixin):
    """Middleware مخصص للـ CORS"""
    
    def process_response(self, request, response):
        # إضافة headers للـ CORS
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'GET, POST, PUT, PATCH, DELETE, OPTIONS'
        response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With'
        response['Access-Control-Max-Age'] = '86400'
        
        return response
    
    def process_request(self, request):
        # التعامل مع طلبات OPTIONS
        if request.method == 'OPTIONS':
            response = JsonResponse({})
            response['Access-Control-Allow-Origin'] = '*'
            response['Access-Control-Allow-Methods'] = 'GET, POST, PUT, PATCH, DELETE, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With'
            response['Access-Control-Max-Age'] = '86400'
            return response
        
        return None

class APISecurityMiddleware(MiddlewareMixin):
    """Middleware للأمان في API"""
    
    def process_response(self, request, response):
        # إضافة headers أمنية
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'
        
        # إزالة headers تكشف معلومات الخادم
        if 'Server' in response:
            del response['Server']
        
        return response

class RateLimitMiddleware(MiddlewareMixin):
    """Middleware بسيط لتحديد معدل الطلبات"""
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.request_counts = {}  # في الإنتاج، استخدم Redis أو cache
        super().__init__(get_response)
    
    def __call__(self, request):
        # تطبيق rate limiting للـ API فقط
        if request.path.startswith('/api/'):
            ip = get_client_ip(request)
            
            # عدد الطلبات المسموحة في الدقيقة
            max_requests = 100
            
            # التحقق من عدد الطلبات
            current_time = int(time.time() / 60)  # دقيقة حالية
            key = f"{ip}:{current_time}"
            
            if key in self.request_counts:
                self.request_counts[key] += 1
                if self.request_counts[key] > max_requests:
                    return JsonResponse(
                        {'error': 'تم تجاوز الحد المسموح من الطلبات'},
                        status=429
                    )
            else:
                self.request_counts[key] = 1
            
            # تنظيف البيانات القديمة
            old_keys = [k for k in self.request_counts.keys() 
                       if int(k.split(':')[1]) < current_time - 5]
            for old_key in old_keys:
                del self.request_counts[old_key]
        
        response = self.get_response(request)
        return response

import time
