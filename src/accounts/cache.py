from django.core.cache import cache
from django.conf import settings
from django.contrib.auth import get_user_model
from typing import List, Set, Optional
import logging

User = get_user_model()
logger = logging.getLogger(__name__)

class PermissionCache:
    """نظام تخزين مؤقت للصلاحيات"""
    
    # Cache key patterns
    USER_PERMISSIONS_KEY = "user_permissions:{user_id}"
    USER_ROLES_KEY = "user_roles:{user_id}"
    ROLE_PERMISSIONS_KEY = "role_permissions:{role_id}"
    PERMISSION_EXISTS_KEY = "permission_exists:{permission_name}"
    
    @classmethod
    def get_user_permissions_cache_key(cls, user_id: int) -> str:
        """إنشاء مفتاح cache لصلاحيات المستخدم"""
        return cls.USER_PERMISSIONS_KEY.format(user_id=user_id)
    
    @classmethod
    def get_user_roles_cache_key(cls, user_id: int) -> str:
        """إنشاء مفتاح cache لأدوار المستخدم"""
        return cls.USER_ROLES_KEY.format(user_id=user_id)
    
    @classmethod
    def get_role_permissions_cache_key(cls, role_id: int) -> str:
        """إنشاء مفتاح cache لصلاحيات الدور"""
        return cls.ROLE_PERMISSIONS_KEY.format(role_id=role_id)
    
    @classmethod
    def get_permission_exists_cache_key(cls, permission_name: str) -> str:
        """إنشاء مفتاح cache للتحقق من وجود صلاحية"""
        return cls.PERMISSION_EXISTS_KEY.format(permission_name=permission_name)
    
    @classmethod
    def get_user_permissions(cls, user_id: int) -> Optional[Set[str]]:
        """الحصول على صلاحيات المستخدم من الـ cache"""
        cache_key = cls.get_user_permissions_cache_key(user_id)
        permissions = cache.get(cache_key)
        
        if permissions is not None:
            logger.debug(f"Cache hit for user {user_id} permissions")
            return set(permissions) if permissions else set()
        
        logger.debug(f"Cache miss for user {user_id} permissions")
        return None
    
    @classmethod
    def set_user_permissions(cls, user_id: int, permissions: Set[str]) -> None:
        """تخزين صلاحيات المستخدم في الـ cache"""
        cache_key = cls.get_user_permissions_cache_key(user_id)
        timeout = getattr(settings, 'USER_PERMISSIONS_CACHE_TIMEOUT', 600)
        
        cache.set(cache_key, list(permissions), timeout)
        logger.debug(f"Cached permissions for user {user_id}: {len(permissions)} permissions")
    
    @classmethod
    def get_user_roles(cls, user_id: int) -> Optional[List[dict]]:
        """الحصول على أدوار المستخدم من الـ cache"""
        cache_key = cls.get_user_roles_cache_key(user_id)
        roles = cache.get(cache_key)
        
        if roles is not None:
            logger.debug(f"Cache hit for user {user_id} roles")
            return roles
        
        logger.debug(f"Cache miss for user {user_id} roles")
        return None
    
    @classmethod
    def set_user_roles(cls, user_id: int, roles: List[dict]) -> None:
        """تخزين أدوار المستخدم في الـ cache"""
        cache_key = cls.get_user_roles_cache_key(user_id)
        timeout = getattr(settings, 'USER_PERMISSIONS_CACHE_TIMEOUT', 600)
        
        cache.set(cache_key, roles, timeout)
        logger.debug(f"Cached roles for user {user_id}: {len(roles)} roles")
    
    @classmethod
    def get_role_permissions(cls, role_id: int) -> Optional[Set[str]]:
        """الحصول على صلاحيات الدور من الـ cache"""
        cache_key = cls.get_role_permissions_cache_key(role_id)
        permissions = cache.get(cache_key)
        
        if permissions is not None:
            logger.debug(f"Cache hit for role {role_id} permissions")
            return set(permissions) if permissions else set()
        
        logger.debug(f"Cache miss for role {role_id} permissions")
        return None
    
    @classmethod
    def set_role_permissions(cls, role_id: int, permissions: Set[str]) -> None:
        """تخزين صلاحيات الدور في الـ cache"""
        cache_key = cls.get_role_permissions_cache_key(role_id)
        timeout = getattr(settings, 'ROLE_PERMISSIONS_CACHE_TIMEOUT', 600)
        
        cache.set(cache_key, list(permissions), timeout)
        logger.debug(f"Cached permissions for role {role_id}: {len(permissions)} permissions")
    
    @classmethod
    def permission_exists(cls, permission_name: str) -> Optional[bool]:
        """التحقق من وجود صلاحية من الـ cache"""
        cache_key = cls.get_permission_exists_cache_key(permission_name)
        exists = cache.get(cache_key)
        
        if exists is not None:
            logger.debug(f"Cache hit for permission existence: {permission_name}")
            return exists
        
        logger.debug(f"Cache miss for permission existence: {permission_name}")
        return None
    
    @classmethod
    def set_permission_exists(cls, permission_name: str, exists: bool) -> None:
        """تخزين معلومات وجود الصلاحية في الـ cache"""
        cache_key = cls.get_permission_exists_cache_key(permission_name)
        timeout = getattr(settings, 'PERMISSION_CACHE_TIMEOUT', 300)
        
        cache.set(cache_key, exists, timeout)
        logger.debug(f"Cached permission existence for {permission_name}: {exists}")
    
    @classmethod
    def invalidate_user_cache(cls, user_id: int) -> None:
        """إلغاء cache المستخدم"""
        user_permissions_key = cls.get_user_permissions_cache_key(user_id)
        user_roles_key = cls.get_user_roles_cache_key(user_id)
        
        cache.delete_many([user_permissions_key, user_roles_key])
        logger.info(f"Invalidated cache for user {user_id}")
    
    @classmethod
    def invalidate_role_cache(cls, role_id: int) -> None:
        """إلغاء cache الدور"""
        role_permissions_key = cls.get_role_permissions_cache_key(role_id)
        cache.delete(role_permissions_key)
        logger.info(f"Invalidated cache for role {role_id}")
    
    @classmethod
    def invalidate_permission_cache(cls, permission_name: str) -> None:
        """إلغاء cache الصلاحية"""
        permission_exists_key = cls.get_permission_exists_cache_key(permission_name)
        cache.delete(permission_exists_key)
        logger.info(f"Invalidated cache for permission {permission_name}")
    
    @classmethod
    def invalidate_all_user_caches(cls) -> None:
        """إلغاء جميع caches المستخدمين (عند تغيير الأدوار أو الصلاحيات)"""
        # في الإنتاج، يمكن استخدام pattern matching مع Redis
        # هنا سنقوم بإلغاء cache محدد عند الحاجة
        logger.info("Invalidating all user caches")
        # يمكن تطبيق هذا بشكل أكثر كفاءة مع Redis patterns
    
    @classmethod
    def warm_up_user_cache(cls, user_id: int) -> None:
        """تحميل cache المستخدم مسبقاً"""
        try:
            from .services import UserService
            user_service = UserService()
            
            # تحميل الصلاحيات
            permissions = user_service.get_user_permissions(user_id)
            cls.set_user_permissions(user_id, set(permissions))
            
            # تحميل الأدوار
            user = User.objects.get(id=user_id)
            roles = [{'id': ur.role.id, 'name': ur.role.name} 
                    for ur in user.user_roles.filter(is_active=True).select_related('role')]
            cls.set_user_roles(user_id, roles)
            
            logger.info(f"Warmed up cache for user {user_id}")
            
        except Exception as e:
            logger.error(f"Failed to warm up cache for user {user_id}: {e}")

class CacheManager:
    """مدير الـ cache العام"""
    
    @staticmethod
    def clear_all_permissions_cache():
        """مسح جميع caches الصلاحيات"""
        try:
            # في الإنتاج، استخدم Redis patterns للمسح الجماعي
            cache.clear()
            logger.info("Cleared all permissions cache")
        except Exception as e:
            logger.error(f"Failed to clear permissions cache: {e}")
    
    @staticmethod
    def get_cache_stats():
        """إحصائيات الـ cache"""
        try:
            # هذا يعتمد على نوع الـ cache المستخدم
            return {
                'cache_backend': settings.CACHES['default']['BACKEND'],
                'location': settings.CACHES['default']['LOCATION'],
                'status': 'active'
            }
        except Exception as e:
            logger.error(f"Failed to get cache stats: {e}")
            return {'status': 'error', 'error': str(e)}
