from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from accounts.models import Role, Permission, UserRole, RolePermission

User = get_user_model()

class Command(BaseCommand):
    help = 'إنشاء بيانات تجريبية للمستخدمين والأدوار والصلاحيات للـ API'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('بدء إنشاء البيانات التجريبية للـ API...'))
        
        # إنشاء الصلاحيات
        permissions_data = [
            {'name': 'قراءة المستخدمين', 'description': 'عرض قائمة المستخدمين عبر API', 'permission_type': 'read'},
            {'name': 'إضافة مستخدمين', 'description': 'إنشاء مستخدمين جدد عبر API', 'permission_type': 'write'},
            {'name': 'تعديل المستخدمين', 'description': 'تعديل بيانات المستخدمين عبر API', 'permission_type': 'write'},
            {'name': 'حذف المستخدمين', 'description': 'حذف المستخدمين عبر API', 'permission_type': 'delete'},
            {'name': 'إدارة الأدوار', 'description': 'إدارة أدوار المستخدمين عبر API', 'permission_type': 'manage'},
            {'name': 'إدارة الصلاحيات', 'description': 'إدارة صلاحيات النظام عبر API', 'permission_type': 'manage'},
            {'name': 'عرض الأنشطة', 'description': 'عرض سجل أنشطة المستخدمين عبر API', 'permission_type': 'read'},
            {'name': 'تعيين الأدوار', 'description': 'تعيين الأدوار للمستخدمين عبر API', 'permission_type': 'manage'},
            {'name': 'قراءة الأدوار', 'description': 'عرض قائمة الأدوار عبر API', 'permission_type': 'read'},
            {'name': 'إنشاء الأدوار', 'description': 'إنشاء أدوار جديدة عبر API', 'permission_type': 'write'},
            {'name': 'تعديل الأدوار', 'description': 'تعديل الأدوار عبر API', 'permission_type': 'write'},
            {'name': 'حذف الأدوار', 'description': 'حذف الأدوار عبر API', 'permission_type': 'delete'},
            {'name': 'قراءة الصلاحيات', 'description': 'عرض قائمة الصلاحيات عبر API', 'permission_type': 'read'},
            {'name': 'إنشاء الصلاحيات', 'description': 'إنشاء صلاحيات جديدة عبر API', 'permission_type': 'write'},
            {'name': 'تعديل الصلاحيات', 'description': 'تعديل الصلاحيات عبر API', 'permission_type': 'write'},
            {'name': 'حذف الصلاحيات', 'description': 'حذف الصلاحيات عبر API', 'permission_type': 'delete'},
        ]
        
        permissions = []
        for perm_data in permissions_data:
            permission, created = Permission.objects.get_or_create(
                name=perm_data['name'],
                defaults=perm_data
            )
            permissions.append(permission)
            if created:
                self.stdout.write(f'تم إنشاء الصلاحية: {permission.name}')
        
        # إنشاء الأدوار
        roles_data = [
            {
                'name': 'مدير عام API',
                'description': 'مدير عام للنظام مع جميع صلاحيات API',
                'permissions': permissions  # جميع الصلاحيات
            },
            {
                'name': 'مدير المستخدمين API',
                'description': 'مدير لإدارة المستخدمين عبر API',
                'permissions': [p for p in permissions if 'المستخدمين' in p.name or 'الأدوار' in p.name]
            },
            {
                'name': 'مستخدم عادي API',
                'description': 'مستخدم عادي مع صلاحيات محدودة للـ API',
                'permissions': [p for p in permissions if p.permission_type == 'read' and 'المستخدمين' not in p.name]
            },
            {
                'name': 'محرر API',
                'description': 'محرر مع صلاحيات القراءة والكتابة للـ API',
                'permissions': [p for p in permissions if p.permission_type in ['read', 'write'] and 'حذف' not in p.name]
            },
            {
                'name': 'مراقب API',
                'description': 'مراقب للنظام مع صلاحيات القراءة فقط',
                'permissions': [p for p in permissions if p.permission_type == 'read']
            }
        ]
        
        roles = []
        for role_data in roles_data:
            role_permissions = role_data.pop('permissions')
            role, created = Role.objects.get_or_create(
                name=role_data['name'],
                defaults=role_data
            )
            roles.append(role)
            
            if created:
                self.stdout.write(f'تم إنشاء الدور: {role.name}')
                
                # تعيين الصلاحيات للدور
                for permission in role_permissions:
                    RolePermission.objects.get_or_create(
                        role=role,
                        permission=permission,
                        defaults={'assigned_by': None}
                    )
        
        # إنشاء المستخدمين التجريبيين
        users_data = [
            {
                'username': 'api_admin',
                'email': '<EMAIL>',
                'first_name': 'مدير',
                'last_name': 'API',
                'password': 'admin123',
                'is_staff': True,
                'is_superuser': True,
                'role': 'مدير عام API'
            },
            {
                'username': 'api_user_manager',
                'email': '<EMAIL>',
                'first_name': 'مدير',
                'last_name': 'المستخدمين',
                'password': 'manager123',
                'role': 'مدير المستخدمين API'
            },
            {
                'username': 'api_editor',
                'email': '<EMAIL>',
                'first_name': 'محرر',
                'last_name': 'API',
                'password': 'editor123',
                'role': 'محرر API'
            },
            {
                'username': 'api_monitor',
                'email': '<EMAIL>',
                'first_name': 'مراقب',
                'last_name': 'النظام',
                'password': 'monitor123',
                'role': 'مراقب API'
            },
            {
                'username': 'api_user',
                'email': '<EMAIL>',
                'first_name': 'مستخدم',
                'last_name': 'عادي',
                'password': 'user123',
                'role': 'مستخدم عادي API'
            }
        ]
        
        for user_data in users_data:
            role_name = user_data.pop('role')
            password = user_data.pop('password')
            
            user, created = User.objects.get_or_create(
                username=user_data['username'],
                defaults=user_data
            )
            
            if created:
                user.set_password(password)
                user.save()
                self.stdout.write(f'تم إنشاء المستخدم: {user.username}')
                
                # تعيين الدور للمستخدم
                role = Role.objects.get(name=role_name)
                UserRole.objects.get_or_create(
                    user=user,
                    role=role,
                    defaults={'assigned_by': None}
                )
                self.stdout.write(f'تم تعيين الدور "{role_name}" للمستخدم {user.username}')
        
        self.stdout.write(self.style.SUCCESS('تم إنجاز إنشاء البيانات التجريبية للـ API بنجاح!'))
        self.stdout.write(self.style.WARNING('معلومات تسجيل الدخول للـ API:'))
        self.stdout.write('مدير API العام: api_admin / admin123')
        self.stdout.write('مدير المستخدمين: api_user_manager / manager123')
        self.stdout.write('محرر API: api_editor / editor123')
        self.stdout.write('مراقب النظام: api_monitor / monitor123')
        self.stdout.write('مستخدم عادي: api_user / user123')
        self.stdout.write('')
        self.stdout.write(self.style.SUCCESS('يمكنك الآن استخدام هذه الحسابات لاختبار API endpoints'))
        self.stdout.write('مثال: POST /api/v1/auth/login/ مع username و password')
