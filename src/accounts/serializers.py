from rest_framework import serializers
from django.contrib.auth import get_user_model, authenticate
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from .models import Role, Permission, UserRole, RolePermission, UserActivity

User = get_user_model()

class UserSerializer(serializers.ModelSerializer):
    """Serializer للمستخدمين"""
    password = serializers.CharField(write_only=True, validators=[validate_password])
    confirm_password = serializers.CharField(write_only=True)
    full_name = serializers.CharField(source='get_full_name', read_only=True)
    roles = serializers.SerializerMethodField()
    permissions = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name', 'full_name',
            'phone_number', 'birth_date', 'profile_picture', 'is_active', 
            'is_staff', 'is_superuser', 'date_joined', 'last_login',
            'password', 'confirm_password', 'roles', 'permissions'
        ]
        extra_kwargs = {
            'password': {'write_only': True},
            'is_staff': {'read_only': True},
            'is_superuser': {'read_only': True},
            'date_joined': {'read_only': True},
            'last_login': {'read_only': True},
        }
    
    def get_roles(self, obj):
        """إرجاع أدوار المستخدم"""
        user_roles = obj.user_roles.filter(is_active=True).select_related('role')
        return [{'id': ur.role.id, 'name': ur.role.name} for ur in user_roles]
    
    def get_permissions(self, obj):
        """إرجاع صلاحيات المستخدم"""
        permissions = obj.get_all_permissions()
        return [{'id': p.id, 'name': p.name, 'type': p.permission_type} for p in permissions]
    
    def validate(self, attrs):
        """التحقق من صحة البيانات"""
        if 'password' in attrs and 'confirm_password' in attrs:
            if attrs['password'] != attrs['confirm_password']:
                raise serializers.ValidationError("كلمات المرور غير متطابقة")
        return attrs
    
    def create(self, validated_data):
        """إنشاء مستخدم جديد"""
        validated_data.pop('confirm_password', None)
        password = validated_data.pop('password')
        user = User.objects.create_user(**validated_data)
        user.set_password(password)
        user.save()
        return user
    
    def update(self, instance, validated_data):
        """تحديث مستخدم"""
        validated_data.pop('confirm_password', None)
        password = validated_data.pop('password', None)
        
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        
        if password:
            instance.set_password(password)
        
        instance.save()
        return instance

class UserCreateSerializer(serializers.ModelSerializer):
    """Serializer لإنشاء مستخدم جديد"""
    password = serializers.CharField(write_only=True, validators=[validate_password])
    confirm_password = serializers.CharField(write_only=True)
    
    class Meta:
        model = User
        fields = [
            'username', 'email', 'first_name', 'last_name',
            'phone_number', 'birth_date', 'password', 'confirm_password'
        ]
    
    def validate(self, attrs):
        if attrs['password'] != attrs['confirm_password']:
            raise serializers.ValidationError("كلمات المرور غير متطابقة")
        return attrs
    
    def create(self, validated_data):
        validated_data.pop('confirm_password')
        password = validated_data.pop('password')
        user = User.objects.create_user(**validated_data)
        user.set_password(password)
        user.save()
        return user

class UserUpdateSerializer(serializers.ModelSerializer):
    """Serializer لتحديث المستخدم"""
    
    class Meta:
        model = User
        fields = [
            'first_name', 'last_name', 'email', 'phone_number', 
            'birth_date', 'profile_picture', 'is_active'
        ]

class ChangePasswordSerializer(serializers.Serializer):
    """Serializer لتغيير كلمة المرور"""
    old_password = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True, validators=[validate_password])
    confirm_password = serializers.CharField(required=True)
    
    def validate(self, attrs):
        if attrs['new_password'] != attrs['confirm_password']:
            raise serializers.ValidationError("كلمات المرور الجديدة غير متطابقة")
        return attrs

class LoginSerializer(serializers.Serializer):
    """Serializer لتسجيل الدخول"""
    username = serializers.CharField()
    password = serializers.CharField()
    
    def validate(self, attrs):
        username = attrs.get('username')
        password = attrs.get('password')
        
        if username and password:
            user = authenticate(username=username, password=password)
            if not user:
                raise serializers.ValidationError('اسم المستخدم أو كلمة المرور غير صحيحة')
            if not user.is_active:
                raise serializers.ValidationError('حساب المستخدم غير نشط')
            attrs['user'] = user
        else:
            raise serializers.ValidationError('يجب إدخال اسم المستخدم وكلمة المرور')
        
        return attrs

class RoleSerializer(serializers.ModelSerializer):
    """Serializer للأدوار"""
    permissions = serializers.SerializerMethodField()
    users_count = serializers.SerializerMethodField()
    permissions_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Role
        fields = [
            'id', 'name', 'description', 'is_active', 'created_at', 
            'updated_at', 'permissions', 'users_count', 'permissions_count'
        ]
        extra_kwargs = {
            'created_at': {'read_only': True},
            'updated_at': {'read_only': True},
        }
    
    def get_permissions(self, obj):
        """إرجاع صلاحيات الدور"""
        role_permissions = obj.role_permissions.filter(is_active=True).select_related('permission')
        return [{'id': rp.permission.id, 'name': rp.permission.name, 'type': rp.permission.permission_type} 
                for rp in role_permissions]
    
    def get_users_count(self, obj):
        """إرجاع عدد المستخدمين في الدور"""
        return obj.role_users.filter(is_active=True).count()
    
    def get_permissions_count(self, obj):
        """إرجاع عدد صلاحيات الدور"""
        return obj.role_permissions.filter(is_active=True).count()

class RoleCreateSerializer(serializers.ModelSerializer):
    """Serializer لإنشاء دور جديد"""
    
    class Meta:
        model = Role
        fields = ['name', 'description', 'is_active']

class PermissionSerializer(serializers.ModelSerializer):
    """Serializer للصلاحيات"""
    roles_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Permission
        fields = [
            'id', 'name', 'description', 'permission_type', 'is_active',
            'created_at', 'updated_at', 'roles_count'
        ]
        extra_kwargs = {
            'created_at': {'read_only': True},
            'updated_at': {'read_only': True},
        }
    
    def get_roles_count(self, obj):
        """إرجاع عدد الأدوار التي تحتوي على هذه الصلاحية"""
        return obj.permission_roles.filter(is_active=True).count()

class UserRoleSerializer(serializers.ModelSerializer):
    """Serializer لأدوار المستخدمين"""
    user_name = serializers.CharField(source='user.username', read_only=True)
    user_full_name = serializers.CharField(source='user.get_full_name', read_only=True)
    role_name = serializers.CharField(source='role.name', read_only=True)
    assigned_by_name = serializers.CharField(source='assigned_by.username', read_only=True)
    is_expired = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = UserRole
        fields = [
            'id', 'user', 'role', 'user_name', 'user_full_name', 'role_name',
            'assigned_at', 'assigned_by', 'assigned_by_name', 'expires_at', 
            'is_active', 'is_expired'
        ]
        extra_kwargs = {
            'assigned_at': {'read_only': True},
        }

class AssignRoleSerializer(serializers.Serializer):
    """Serializer لتعيين دور لمستخدم"""
    user_id = serializers.IntegerField()
    role_id = serializers.IntegerField()
    expires_at = serializers.DateTimeField(required=False, allow_null=True)
    
    def validate_user_id(self, value):
        try:
            User.objects.get(id=value)
        except User.DoesNotExist:
            raise serializers.ValidationError("المستخدم غير موجود")
        return value
    
    def validate_role_id(self, value):
        try:
            Role.objects.get(id=value)
        except Role.DoesNotExist:
            raise serializers.ValidationError("الدور غير موجود")
        return value

class AssignPermissionSerializer(serializers.Serializer):
    """Serializer لتعيين صلاحية لدور"""
    role_id = serializers.IntegerField()
    permission_id = serializers.IntegerField()
    
    def validate_role_id(self, value):
        try:
            Role.objects.get(id=value)
        except Role.DoesNotExist:
            raise serializers.ValidationError("الدور غير موجود")
        return value
    
    def validate_permission_id(self, value):
        try:
            Permission.objects.get(id=value)
        except Permission.DoesNotExist:
            raise serializers.ValidationError("الصلاحية غير موجودة")
        return value

class UserActivitySerializer(serializers.ModelSerializer):
    """Serializer لأنشطة المستخدمين"""
    user_name = serializers.CharField(source='user.username', read_only=True)
    performed_by_name = serializers.CharField(source='performed_by.username', read_only=True)
    action_display = serializers.CharField(source='get_action_display', read_only=True)
    
    class Meta:
        model = UserActivity
        fields = [
            'id', 'user', 'user_name', 'action', 'action_display', 'description',
            'ip_address', 'user_agent', 'performed_by', 'performed_by_name', 'timestamp'
        ]
        extra_kwargs = {
            'timestamp': {'read_only': True},
        }

class UserProfileSerializer(serializers.ModelSerializer):
    """Serializer لملف المستخدم الشخصي"""
    full_name = serializers.CharField(source='get_full_name', read_only=True)
    roles = serializers.SerializerMethodField()
    permissions = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name', 'full_name',
            'phone_number', 'birth_date', 'profile_picture', 'date_joined',
            'last_login', 'roles', 'permissions'
        ]
        extra_kwargs = {
            'username': {'read_only': True},
            'date_joined': {'read_only': True},
            'last_login': {'read_only': True},
        }
    
    def get_roles(self, obj):
        user_roles = obj.user_roles.filter(is_active=True).select_related('role')
        return [ur.role.name for ur in user_roles]
    
    def get_permissions(self, obj):
        permissions = obj.get_all_permissions()
        return [p.name for p in permissions]
