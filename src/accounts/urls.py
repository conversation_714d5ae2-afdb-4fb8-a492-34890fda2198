from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework_simplejwt.views import TokenRefreshView
from . import views

# إنشاء router للـ ViewSets
router = DefaultRouter()
router.register(r'users', views.UserViewSet, basename='user')
router.register(r'roles', views.RoleViewSet, basename='role')
router.register(r'permissions', views.PermissionViewSet, basename='permission')

app_name = 'accounts'

urlpatterns = [
    # Authentication endpoints
    path('auth/login/', views.LoginView.as_view(), name='login'),
    path('auth/logout/', views.LogoutView.as_view(), name='logout'),
    path('auth/refresh/', TokenRefreshView.as_view(), name='token_refresh'),

    # Profile endpoints
    path('profile/', views.UserProfileView.as_view(), name='profile'),
    path('change-password/', views.ChangePasswordView.as_view(), name='change_password'),

    # Role assignment endpoints
    path('assign-role/', views.assign_role_to_user, name='assign_role'),
    path('remove-role/', views.remove_role_from_user, name='remove_role'),

    # Permission assignment endpoints
    path('assign-permission/', views.assign_permission_to_role, name='assign_permission'),
    path('remove-permission/', views.remove_permission_from_role, name='remove_permission'),

    # Activities endpoint
    path('activities/', views.UserActivitiesView.as_view(), name='activities'),

    # Include router URLs
    path('', include(router.urls)),
]